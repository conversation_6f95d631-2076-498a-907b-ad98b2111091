import { useState, useEffect } from 'react';
import { Save, Plus, Trash2, MoveUp, MoveDown, Pencil } from 'lucide-react';
import { router, usePage } from '@inertiajs/react';
import { useToast } from '@/components/ui/use-toast';

import AdminLayout from './layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';

// Define types for services data
interface Service {
    id: number;
    title: string;
    description: string;
    icon: string;
    order: number;
    is_featured: boolean;
    is_active: boolean;
    created_at?: string;
    updated_at?: string;
    [key: string]: any; // Index signature for router compatibility
}

// Default empty service for new service form
const emptyService: Service = {
    id: 0,
    title: '',
    description: '',
    icon: '',
    order: 0,
    is_featured: true,
    is_active: true
};

interface ServicesProps {
    services?: Service[];
}

export default function Services({ services: backendServices = [] }: ServicesProps) {
    const { toast } = useToast();
    const { errors } = usePage().props as any;
    
    // State for services
    const [services, setServices] = useState<Service[]>(backendServices || []);
    
    // State for service form
    const [isOpen, setIsOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [currentService, setCurrentService] = useState<Service>(emptyService);
    const [isSaving, setIsSaving] = useState(false);
    
    // Show error toast if there are validation errors
    useEffect(() => {
        if (errors && Object.keys(errors).length > 0) {
            toast({
                title: "Validation Error",
                description: "Please check the form for errors.",
                variant: "destructive"
            });
        }
    }, [errors, toast]);
    
    // Open new service form
    const openNewServiceForm = () => {
        setCurrentService({
            ...emptyService,
            order: services.length > 0 ? Math.max(...services.map(s => s.order)) + 1 : 1
        });
        setIsEditing(false);
        setIsOpen(true);
    };
    
    // Open edit service form
    const openEditServiceForm = (service: Service) => {
        setCurrentService(service);
        setIsEditing(true);
        setIsOpen(true);
    };
    
    // Handle input change
    const handleInputChange = (key: keyof Service, value: any) => {
        setCurrentService(prev => ({
            ...prev,
            [key]: value
        }));
    };
    
    // Save service
    const saveService = () => {
        setIsSaving(true);
        
        if (isEditing) {
            // Update existing service
            router.put(`/admin/services/${currentService.id}`, currentService as Record<string, any>, {
                onSuccess: () => {
                    setIsSaving(false);
                    setIsOpen(false);
                    toast({
                        title: "Service Updated",
                        description: "Service has been updated successfully."
                    });
                },
                onError: () => {
                    setIsSaving(false);
                    toast({
                        title: "Error saving changes",
                        description: "There was a problem saving your changes. Please try again.",
                        variant: "destructive"
                    });
                }
            });
        } else {
            // Create new service
            router.post('/admin/services', currentService as Record<string, any>, {
                onSuccess: () => {
                    setIsSaving(false);
                    setIsOpen(false);
                    toast({
                        title: "Service Created",
                        description: "New service has been created successfully."
                    });
                },
                onError: () => {
                    setIsSaving(false);
                    toast({
                        title: "Error saving changes",
                        description: "There was a problem saving your changes. Please try again.",
                        variant: "destructive"
                    });
                }
            });
        }
    };
    
    // Delete service
    const deleteService = (id: number) => {
        if (confirm('Are you sure you want to delete this service?')) {
            router.delete(`/admin/services/${id}`, {
                onSuccess: () => {
                    toast({
                        title: "Service Deleted",
                        description: "Service has been deleted successfully."
                    });
                },
                onError: () => {
                    toast({
                        title: "Error deleting service",
                        description: "There was a problem deleting the service. Please try again.",
                        variant: "destructive"
                    });
                }
            });
        }
    };
    
    // Move service up in order
    const moveServiceUp = (index: number) => {
        if (index === 0) return;
        
        const updatedServices = [...services];
        const temp = updatedServices[index].order;
        updatedServices[index].order = updatedServices[index - 1].order;
        updatedServices[index - 1].order = temp;
        
        // Update order in database
        router.put(`/admin/services/${updatedServices[index].id}/order`, {
            order: updatedServices[index].order
        });
        
        router.put(`/admin/services/${updatedServices[index - 1].id}/order`, {
            order: updatedServices[index - 1].order
        });
        
        // Update local state
        setServices([...updatedServices].sort((a, b) => a.order - b.order));
    };
    
    // Move service down in order
    const moveServiceDown = (index: number) => {
        if (index === services.length - 1) return;
        
        const updatedServices = [...services];
        const temp = updatedServices[index].order;
        updatedServices[index].order = updatedServices[index + 1].order;
        updatedServices[index + 1].order = temp;
        
        // Update order in database
        router.put(`/admin/services/${updatedServices[index].id}/order`, {
            order: updatedServices[index].order
        });
        
        router.put(`/admin/services/${updatedServices[index + 1].id}/order`, {
            order: updatedServices[index + 1].order
        });
        
        // Update local state
        setServices([...updatedServices].sort((a, b) => a.order - b.order));
    };
    
    // Toggle service featured status
    const toggleServiceFeatured = (service: Service) => {
        const updatedService = { ...service, is_featured: !service.is_featured };
        
        router.put(`/admin/services/${service.id}`, updatedService, {
            onSuccess: () => {
                toast({
                    title: updatedService.is_featured ? "Service Featured" : "Service Unfeatured",
                    description: `Service has been ${updatedService.is_featured ? 'featured' : 'unfeatured'} successfully.`
                });
            }
        });
    };
    
    // Toggle service active status
    const toggleServiceActive = (service: Service) => {
        const updatedService = { ...service, is_active: !service.is_active };
        
        router.put(`/admin/services/${service.id}`, updatedService, {
            onSuccess: () => {
                toast({
                    title: updatedService.is_active ? "Service Activated" : "Service Deactivated",
                    description: `Service has been ${updatedService.is_active ? 'activated' : 'deactivated'} successfully.`
                });
            }
        });
    };
    
    return (
        <AdminLayout title="Services" currentPage="services">
            <div className="mb-6 flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Services</h1>
                    <p className="text-gray-500 dark:text-gray-400">Manage your services</p>
                </div>
                <Button onClick={openNewServiceForm} className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Service
                </Button>
            </div>
            
            {/* Services list */}
            <div className="space-y-4">
                {services.length === 0 ? (
                    <Card>
                        <CardContent className="flex flex-col items-center justify-center p-6">
                            <p className="mb-4 text-center text-gray-500 dark:text-gray-400">
                                No services found. Add your first service to get started.
                            </p>
                            <Button onClick={openNewServiceForm} variant="outline">
                                <Plus className="mr-2 h-4 w-4" />
                                Add Service
                            </Button>
                        </CardContent>
                    </Card>
                ) : (
                    services.map((service, index) => (
                        <Card key={service.id} className={service.is_active ? "" : "opacity-60"}>
                            <CardHeader className="pb-2">
                                <div className="flex items-center justify-between">
                                    <CardTitle className="text-xl">{service.title}</CardTitle>
                                    <div className="flex items-center gap-2">
                                        <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            onClick={() => toggleServiceFeatured(service)}
                                            title={service.is_featured ? "Unfeature" : "Feature"}
                                        >
                                            <svg 
                                                width="15" 
                                                height="15" 
                                                viewBox="0 0 15 15" 
                                                fill="none" 
                                                xmlns="http://www.w3.org/2000/svg"
                                                className={service.is_featured ? "fill-yellow-500" : "fill-gray-300"}
                                            >
                                                <path d="M7.5 0L9.18386 5.18237H14.6329L10.2245 8.38525L11.9084 13.5676L7.5 10.3647L3.09161 13.5676L4.77547 8.38525L0.367076 5.18237H5.81614L7.5 0Z" />
                                            </svg>
                                        </Button>
                                        <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            onClick={() => toggleServiceActive(service)}
                                            title={service.is_active ? "Deactivate" : "Activate"}
                                        >
                                            <svg 
                                                width="15" 
                                                height="15" 
                                                viewBox="0 0 15 15" 
                                                fill="none" 
                                                xmlns="http://www.w3.org/2000/svg"
                                                className={service.is_active ? "fill-green-500" : "fill-gray-300"}
                                            >
                                                <path d="M7.5 0.875C5.49797 0.875 3.58394 1.67165 2.16637 3.08923C0.748797 4.5068 0 6.4462 0 8.44824C0 10.4503 0.748797 12.3897 2.16637 13.8072C3.58394 15.2248 5.49797 16.0215 7.5 16.0215C9.50203 16.0215 11.4161 15.2248 12.8336 13.8072C14.2512 12.3897 15 10.4503 15 8.44824C15 6.4462 14.2512 4.5068 12.8336 3.08923C11.4161 1.67165 9.50203 0.875 7.5 0.875ZM11.25 9.07324C11.25 9.19799 11.1974 9.31773 11.1036 9.40556C11.0098 9.49338 10.8859 9.54074 10.7578 9.54074H4.24219C4.11395 9.54074 3.99005 9.49338 3.89636 9.40556C3.80267 9.31773 3.75 9.19799 3.75 9.07324V7.82324C3.75 7.69849 3.80267 7.57875 3.89636 7.49093C3.99005 7.4031 4.11395 7.35574 4.24219 7.35574H10.7578C10.8859 7.35574 11.0098 7.4031 11.1036 7.49093C11.1974 7.57875 11.25 7.69849 11.25 7.82324V9.07324Z" />
                                            </svg>
                                        </Button>
                                        <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            onClick={() => openEditServiceForm(service)}
                                            title="Edit"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <Button 
                                            variant="ghost" 
                                            size="icon" 
                                            onClick={() => deleteService(service.id)}
                                            title="Delete"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                        <div className="flex flex-col">
                                            <Button 
                                                variant="ghost" 
                                                size="icon" 
                                                onClick={() => moveServiceUp(index)}
                                                disabled={index === 0}
                                                className="h-6"
                                                title="Move Up"
                                            >
                                                <MoveUp className="h-3 w-3" />
                                            </Button>
                                            <Button 
                                                variant="ghost" 
                                                size="icon" 
                                                onClick={() => moveServiceDown(index)}
                                                disabled={index === services.length - 1}
                                                className="h-6"
                                                title="Move Down"
                                            >
                                                <MoveDown className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="flex items-start gap-3">
                                    {service.icon && (
                                        <div className="flex-shrink-0 mt-1">
                                            <div className="flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 service-icon">
                                                {/* Render based on icon type */}
                                                {(() => {
                                                    // Simple check for emoji (most common emojis are 1-2 characters)
                                                    if (service.icon.length <= 2) {
                                                        return <span className="text-xl">{service.icon}</span>;
                                                    }
                                                    // Check if it's a Font Awesome icon
                                                    else if (service.icon.startsWith('fa-')) {
                                                        return <i className={`fas ${service.icon}`}></i>;
                                                    }
                                                    // Check if it's a Font Awesome brand icon
                                                    else if (service.icon.startsWith('fab-')) {
                                                        return <i className={`fab ${service.icon.replace('fab-', 'fa-')}`}></i>;
                                                    }
                                                    // Check if it's a Material icon
                                                    else if (service.icon.startsWith('material-')) {
                                                        return <i className="material-icons">{service.icon.replace('material-', '')}</i>;
                                                    }
                                                    // Try to render as a Font Awesome icon by default
                                                    else {
                                                        return (
                                                            <>
                                                                <i className={`fas fa-${service.icon}`}></i>
                                                                <span className="sr-only">{service.icon}</span>
                                                            </>
                                                        );
                                                    }
                                                })()}
                                            </div>
                                        </div>
                                    )}
                                    <div className="flex-1">
                                        <p className="text-gray-500 dark:text-gray-400">{service.description}</p>
                                        {service.icon && (
                                            <div className="mt-2">
                                                <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Icon: </span>
                                                <code className="rounded bg-gray-100 px-1 py-0.5 text-sm dark:bg-gray-800">{service.icon}</code>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))
                )}
            </div>
            
            {/* Service form dialog */}
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>{isEditing ? 'Edit Service' : 'Add New Service'}</DialogTitle>
                        <DialogDescription>
                            {isEditing ? 'Update the details of your service.' : 'Add a new service to your portfolio.'}
                        </DialogDescription>
                    </DialogHeader>
                    
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="title" className="text-right">
                                Title
                            </Label>
                            <Input
                                id="title"
                                value={currentService.title}
                                onChange={(e) => handleInputChange('title', e.target.value)}
                                className="col-span-3"
                            />
                            {errors.title && (
                                <p className="col-span-3 col-start-2 text-sm text-red-500">{errors.title}</p>
                            )}
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="description" className="text-right">
                                Description
                            </Label>
                            <Textarea
                                id="description"
                                value={currentService.description}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                className="col-span-3"
                                rows={4}
                            />
                            {errors.description && (
                                <p className="col-span-3 col-start-2 text-sm text-red-500">{errors.description}</p>
                            )}
                        </div>
                        
                        <div className="grid grid-cols-4 items-start gap-4">
                            <Label htmlFor="icon" className="text-right pt-2">
                                Icon
                            </Label>
                            <div className="col-span-3 space-y-3">
                                <Input
                                    id="icon"
                                    value={currentService.icon}
                                    onChange={(e) => handleInputChange('icon', e.target.value)}
                                    className="w-full"
                                    placeholder="icon-name"
                                />
                                <div className="text-xs text-gray-500 space-y-1">
                                    <p>Enter one of the following:</p>
                                    <ul className="list-disc pl-5 space-y-1">
                                        <li>Emoji: "🚀", "💻", "📱"</li>
                                        <li>Font Awesome icon: "fa-code", "fa-laptop", "fa-mobile"</li>
                                        <li>Font Awesome brand icon: "fab-react", "fab-github"</li>
                                        <li>Material icon: "material-code", "material-computer"</li>
                                        <li>Simple name: "code", "design" (will use Font Awesome)</li>
                                    </ul>
                                    <div className="mt-2 flex flex-wrap gap-2">
                                        {['code', 'laptop', 'mobile', 'palette', 'camera', 'server'].map(icon => (
                                            <button 
                                                key={icon}
                                                type="button"
                                                onClick={() => handleInputChange('icon', icon)}
                                                className="inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded-md bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
                                            >
                                                <i className={`fas fa-${icon} mr-1`}></i>
                                                {icon}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                                {currentService.icon && (
                                    <div className="flex items-center space-x-2">
                                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400 service-icon">
                                            {(() => {
                                                if (currentService.icon.length <= 2) {
                                                    return <span className="text-lg">{currentService.icon}</span>;
                                                } else if (currentService.icon.startsWith('fa-')) {
                                                    return <i className={`fas ${currentService.icon}`}></i>;
                                                } else if (currentService.icon.startsWith('fab-')) {
                                                    return <i className={`fab ${currentService.icon.replace('fab-', 'fa-')}`}></i>;
                                                } else if (currentService.icon.startsWith('material-')) {
                                                    return <i className="material-icons">{currentService.icon.replace('material-', '')}</i>;
                                                } else {
                                                    return <i className={`fas fa-${currentService.icon}`}></i>;
                                                }
                                            })()}
                                        </div>
                                        <span className="text-sm">Icon preview</span>
                                    </div>
                                )}
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="order" className="text-right">
                                Order
                            </Label>
                            <Input
                                id="order"
                                type="number"
                                value={currentService.order.toString()}
                                onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 0)}
                                className="col-span-3"
                            />
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="featured" className="text-right">
                                Featured
                            </Label>
                            <div className="flex items-center space-x-2 col-span-3">
                                <Switch
                                    id="featured"
                                    checked={currentService.is_featured}
                                    onCheckedChange={(checked) => handleInputChange('is_featured', checked)}
                                />
                                <Label htmlFor="featured" className="text-sm font-normal">
                                    Show this service in featured section
                                </Label>
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="active" className="text-right">
                                Active
                            </Label>
                            <div className="flex items-center space-x-2 col-span-3">
                                <Switch
                                    id="active"
                                    checked={currentService.is_active}
                                    onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                                />
                                <Label htmlFor="active" className="text-sm font-normal">
                                    Show this service on the website
                                </Label>
                            </div>
                        </div>
                    </div>
                    
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsOpen(false)}>
                            Cancel
                        </Button>
                        <Button onClick={saveService} disabled={isSaving}>
                            {isSaving ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="mr-2 h-4 w-4" />
                                    {isEditing ? 'Update Service' : 'Save Service'}
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
