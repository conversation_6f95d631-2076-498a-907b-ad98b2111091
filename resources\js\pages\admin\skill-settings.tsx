import { useState, useEffect } from 'react';
import { router, usePage } from '@inertiajs/react';
import { PageProps } from '@inertiajs/core';
import { Settings, Sliders, Save, Info } from 'lucide-react';

import AdminLayout from './layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

// Interface for the page props
interface SkillSettingsPageProps extends PageProps {
    settings: {
        id?: number;
        skills_visualization_type: string;
        skills_primary_color: string;
        skills_secondary_color: string;
        skills_show_categories: boolean;
        skills_show_radar: boolean;
        skills_show_bars: boolean;
        skills_max_display: number;
        skills_custom_css: string;
    };
    title: string;
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: any; // Add index signature for string keys
}

export default function SkillSettings() {
    const { settings: initialSettings, flash } = usePage<SkillSettingsPageProps>().props;
    
    // Default settings if none exist
    const defaultSettings = {
        skills_visualization_type: 'radar',
        skills_primary_color: '#3b82f6',
        skills_secondary_color: '#8b5cf6',
        skills_show_categories: true,
        skills_show_radar: true,
        skills_show_bars: true,
        skills_max_display: 8,
        skills_custom_css: ''
    };
    
    const [settings, setSettings] = useState(initialSettings || defaultSettings);
    const [saveSuccess, setSaveSuccess] = useState(false);
    const [activeTab, setActiveTab] = useState('general');
    
    // Display flash messages from the server
    useEffect(() => {
        if (flash?.success) {
            setSaveSuccess(true);
            
            // Hide success message after 3 seconds
            setTimeout(() => {
                setSaveSuccess(false);
            }, 3000);
        }
    }, [flash]);
    
    // Handle form submission
    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        router.post('/admin/skill-settings', settings, {
            onSuccess: () => {
                setSaveSuccess(true);
                setTimeout(() => setSaveSuccess(false), 3000);
            }
        });
    };
    
    return (
        <AdminLayout title="Skill Settings" currentPage="skill-settings">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Skill Settings</h1>
                    <p className="text-gray-500 dark:text-gray-400">Configure how skills are displayed on your portfolio</p>
                </div>
                
                <Button onClick={handleSubmit}>
                    <Save className="mr-2 h-4 w-4" />
                    Save Settings
                </Button>
            </div>
            
            {/* Success message */}
            {saveSuccess && (
                <Alert className="mb-6 bg-green-50 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <AlertDescription>Settings saved successfully!</AlertDescription>
                </Alert>
            )}
            
            <form onSubmit={handleSubmit}>
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="mb-6">
                        <TabsTrigger value="general">General</TabsTrigger>
                        <TabsTrigger value="visualization">Visualization</TabsTrigger>
                        <TabsTrigger value="advanced">Advanced</TabsTrigger>
                    </TabsList>
                    
                    {/* General Settings */}
                    <TabsContent value="general">
                        <Card>
                            <CardHeader>
                                <CardTitle>General Settings</CardTitle>
                                <CardDescription>Configure basic settings for your skills section</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Max Skills to Display */}
                                    <div className="space-y-2">
                                        <Label htmlFor="skills_max_display">Maximum Skills to Display</Label>
                                        <Input
                                            id="skills_max_display"
                                            type="number"
                                            min="4"
                                            max="20"
                                            value={settings.skills_max_display}
                                            onChange={(e) => setSettings({...settings, skills_max_display: parseInt(e.target.value)})}
                                        />
                                        <p className="text-sm text-gray-500">Limit the number of skills shown in visualizations</p>
                                    </div>
                                    
                                    {/* Visualization Type */}
                                    <div className="space-y-2">
                                        <Label htmlFor="skills_visualization_type">Visualization Type</Label>
                                        <Select 
                                            value={settings.skills_visualization_type} 
                                            onValueChange={(value) => setSettings({...settings, skills_visualization_type: value})}
                                        >
                                            <SelectTrigger id="skills_visualization_type">
                                                <SelectValue placeholder="Select visualization type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="radar">Radar Chart</SelectItem>
                                                <SelectItem value="bars">Bar Chart</SelectItem>
                                                <SelectItem value="bubble">Bubble Chart</SelectItem>
                                                <SelectItem value="grid">Grid Layout</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        <p className="text-sm text-gray-500">Choose how skills are visualized</p>
                                    </div>
                                </div>
                                
                                <Separator />
                                
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    {/* Show Categories */}
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label htmlFor="skills_show_categories" className="block mb-1">Show Skill Categories</Label>
                                            <p className="text-sm text-gray-500">Display skills grouped by category</p>
                                        </div>
                                        <Switch 
                                            id="skills_show_categories"
                                            checked={settings.skills_show_categories}
                                            onCheckedChange={(checked) => setSettings({...settings, skills_show_categories: checked})}
                                        />
                                    </div>
                                    
                                    {/* Show Radar Chart */}
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label htmlFor="skills_show_radar" className="block mb-1">Show Radar Chart</Label>
                                            <p className="text-sm text-gray-500">Display the radar chart visualization</p>
                                        </div>
                                        <Switch 
                                            id="skills_show_radar"
                                            checked={settings.skills_show_radar}
                                            onCheckedChange={(checked) => setSettings({...settings, skills_show_radar: checked})}
                                        />
                                    </div>
                                    
                                    {/* Show Progress Bars */}
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <Label htmlFor="skills_show_bars" className="block mb-1">Show Progress Bars</Label>
                                            <p className="text-sm text-gray-500">Display skill proficiency as progress bars</p>
                                        </div>
                                        <Switch 
                                            id="skills_show_bars"
                                            checked={settings.skills_show_bars}
                                            onCheckedChange={(checked) => setSettings({...settings, skills_show_bars: checked})}
                                        />
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                    
                    {/* Visualization Settings */}
                    <TabsContent value="visualization">
                        <Card>
                            <CardHeader>
                                <CardTitle>Visualization Settings</CardTitle>
                                <CardDescription>Customize the appearance of your skills visualizations</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Primary Color */}
                                    <div className="space-y-2">
                                        <Label htmlFor="skills_primary_color">Primary Color</Label>
                                        <div className="flex gap-2">
                                            <Input
                                                id="skills_primary_color"
                                                type="color"
                                                value={settings.skills_primary_color}
                                                onChange={(e) => setSettings({...settings, skills_primary_color: e.target.value})}
                                                className="w-16 h-10 p-1"
                                            />
                                            <Input
                                                type="text"
                                                value={settings.skills_primary_color}
                                                onChange={(e) => setSettings({...settings, skills_primary_color: e.target.value})}
                                                className="flex-1"
                                                placeholder="#3b82f6"
                                            />
                                        </div>
                                        <p className="text-sm text-gray-500">Main color for development skills</p>
                                    </div>
                                    
                                    {/* Secondary Color */}
                                    <div className="space-y-2">
                                        <Label htmlFor="skills_secondary_color">Secondary Color</Label>
                                        <div className="flex gap-2">
                                            <Input
                                                id="skills_secondary_color"
                                                type="color"
                                                value={settings.skills_secondary_color}
                                                onChange={(e) => setSettings({...settings, skills_secondary_color: e.target.value})}
                                                className="w-16 h-10 p-1"
                                            />
                                            <Input
                                                type="text"
                                                value={settings.skills_secondary_color}
                                                onChange={(e) => setSettings({...settings, skills_secondary_color: e.target.value})}
                                                className="flex-1"
                                                placeholder="#8b5cf6"
                                            />
                                        </div>
                                        <p className="text-sm text-gray-500">Main color for design skills</p>
                                    </div>
                                </div>
                                
                                <div className="mt-6">
                                    <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4">
                                        <div className="flex items-center gap-2 mb-2">
                                            <Info className="h-5 w-5 text-blue-500" />
                                            <h3 className="font-medium">Visualization Preview</h3>
                                        </div>
                                        <p className="text-sm text-gray-500 mb-4">
                                            Changes to colors and visualization settings will be reflected on your portfolio's front page.
                                        </p>
                                        <div className="aspect-video bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                            <p className="text-gray-500">Preview will be visible on the front page</p>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                    
                    {/* Advanced Settings */}
                    <TabsContent value="advanced">
                        <Card>
                            <CardHeader>
                                <CardTitle>Advanced Settings</CardTitle>
                                <CardDescription>Additional customization options for developers</CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="skills_custom_css">Custom CSS</Label>
                                        <Textarea
                                            id="skills_custom_css"
                                            value={settings.skills_custom_css}
                                            onChange={(e) => setSettings({...settings, skills_custom_css: e.target.value})}
                                            placeholder=".skill-item { /* your custom styles */ }"
                                            className="font-mono h-48"
                                        />
                                        <p className="text-sm text-gray-500">Add custom CSS to style the skills section</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
                
                <div className="mt-6 flex justify-end">
                    <Button type="submit">
                        <Save className="mr-2 h-4 w-4" />
                        Save Settings
                    </Button>
                </div>
            </form>
        </AdminLayout>
    );
}
