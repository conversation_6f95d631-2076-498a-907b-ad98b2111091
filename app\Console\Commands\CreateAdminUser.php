<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-admin-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create an admin user for the application';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $user = User::where('email', '<EMAIL>')->first();
        
        if ($user) {
            $this->info('Admin user already exists!');
            return;
        }
        
        $user = new User();
        $user->name = 'Admin User';
        $user->email = '<EMAIL>';
        $user->password = Hash::make('password');
        $user->email_verified_at = now();
        $user->save();
        
        $this->info('Admin user created successfully!');
    }
}
