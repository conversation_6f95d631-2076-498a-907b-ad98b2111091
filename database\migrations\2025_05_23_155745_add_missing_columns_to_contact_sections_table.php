<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contact_sections', function (Blueprint $table) {
            // Add missing columns
            if (!Schema::hasColumn('contact_sections', 'title')) {
                $table->string('title')->nullable();
            }
            if (!Schema::hasColumn('contact_sections', 'subtitle')) {
                $table->string('subtitle')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contact_sections', function (Blueprint $table) {
            $table->dropColumn(['title', 'subtitle']);
        });
    }
};
