<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SkillSetting extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'skills_visualization_type',
        'skills_primary_color',
        'skills_secondary_color',
        'skills_show_categories',
        'skills_show_radar',
        'skills_show_bars',
        'skills_max_display',
        'skills_custom_css',
        'skills_animate_points'
    ];
    
    protected $casts = [
        'skills_show_categories' => 'boolean',
        'skills_show_radar' => 'boolean',
        'skills_show_bars' => 'boolean',
        'skills_max_display' => 'integer',
        'skills_animate_points' => 'boolean'
    ];
}
