<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SkillSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SkillSettingsController extends Controller
{
    /**
     * Display the skill settings page
     */
    public function index()
    {
        $settings = SkillSetting::first() ?? new SkillSetting([
            'skills_visualization_type' => 'radar',
            'skills_primary_color' => '#3b82f6',
            'skills_secondary_color' => '#8b5cf6',
            'skills_show_categories' => true,
            'skills_show_radar' => true,
            'skills_show_bars' => true,
            'skills_max_display' => 8,
            'skills_custom_css' => ''
        ]);
        
        return Inertia::render('admin/skill-settings', [
            'title' => 'Skill Settings',
            'settings' => $settings
        ]);
    }

    /**
     * Update the skill settings
     */
    public function update(Request $request)
    {
        // Debug request data
        \Log::info('Skill Settings Update Request:', ['data' => $request->all()]);
        
        $validator = Validator::make($request->all(), [
            'skills_visualization_type' => 'required|string|in:radar,bars,bubble,grid',
            'skills_primary_color' => 'required|string',
            'skills_secondary_color' => 'required|string',
            'skills_show_categories' => 'boolean',
            'skills_show_radar' => 'boolean',
            'skills_show_bars' => 'boolean',
            'skills_max_display' => 'required|integer|min:4|max:20',
            'skills_custom_css' => 'nullable|string'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed:', ['errors' => $validator->errors()->toArray()]);
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        try {
            $settings = SkillSetting::first();
            
            if (!$settings) {
                $settings = new SkillSetting();
            }
            
            $settings->skills_visualization_type = $request->skills_visualization_type;
            $settings->skills_primary_color = $request->skills_primary_color;
            $settings->skills_secondary_color = $request->skills_secondary_color;
            $settings->skills_show_categories = $request->has('skills_show_categories') ? $request->skills_show_categories : false;
            $settings->skills_show_radar = $request->has('skills_show_radar') ? $request->skills_show_radar : false;
            $settings->skills_show_bars = $request->has('skills_show_bars') ? $request->skills_show_bars : false;
            $settings->skills_max_display = $request->skills_max_display;
            $settings->skills_custom_css = $request->skills_custom_css;
            
            $settings->save();
            
            \Log::info('Skill settings updated successfully', ['id' => $settings->id]);
            return redirect()->back()->with('success', 'Skill settings updated successfully');
        } catch (\Exception $e) {
            \Log::error('Error updating skill settings:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to update skill settings: ' . $e->getMessage()])->withInput();
        }
    }
}
