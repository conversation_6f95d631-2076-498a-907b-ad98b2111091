import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Upload, X } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import axios from 'axios';

interface FileUploadProps {
    onFileUploaded: (filePath: string) => void;
    currentValue?: string;
    accept?: string;
    label?: string;
    className?: string;
    uploadPath?: string;
}

export function FileUpload({
    onFileUploaded,
    currentValue = '',
    accept = 'image/*',
    label = 'Upload File',
    className = '',
    uploadPath = '/admin/upload'
}: FileUploadProps) {
    const [isUploading, setIsUploading] = useState(false);
    const [preview, setPreview] = useState<string | undefined>(currentValue || undefined);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const { toast } = useToast();

    // Update preview when currentValue changes
    useEffect(() => {
        if (currentValue) {
            setPreview(currentValue);
        }
    }, [currentValue]);

    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) return;

        // Show preview
        const reader = new FileReader();
        reader.onload = (e) => {
            setPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);

        // Upload file
        setIsUploading(true);
        const formData = new FormData();
        formData.append('file', file);

        try {
            console.log('Uploading file:', file.name, 'to path:', uploadPath);
            
            const response = await axios.post(uploadPath, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            console.log('Upload response:', response.data);

            if (response.data.success) {
                const uploadedPath = response.data.path;
                console.log('File uploaded successfully. Path:', uploadedPath);
                onFileUploaded(uploadedPath);
                toast({
                    title: 'File Uploaded',
                    description: 'File has been uploaded successfully.',
                });
            } else {
                throw new Error(response.data.message || 'Upload failed');
            }
        } catch (error) {
            console.error('Upload error:', error);
            let errorMessage = 'Failed to upload file';
            
            if (error instanceof Error) {
                errorMessage = error.message;
            } else if (axios.isAxiosError(error) && error.response) {
                errorMessage = `Server error: ${error.response.status} - ${error.response.data?.message || error.message}`;
                console.error('Server response:', error.response.data);
            }
            
            toast({
                title: 'Upload Failed',
                description: errorMessage,
                variant: 'destructive',
            });
            // Reset preview if upload fails
            setPreview(currentValue || undefined);
        } finally {
            setIsUploading(false);
        }
    };

    const clearFile = () => {
        setPreview(undefined);
        onFileUploaded('');
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    return (
        <div className={`space-y-2 ${className}`}>
            <div className="flex items-center gap-2">
                <Button
                    type="button"
                    variant="outline"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={isUploading}
                    className="flex items-center gap-2"
                >
                    <Upload className="h-4 w-4" />
                    {isUploading ? 'Uploading...' : label}
                </Button>
                {preview && (
                    <Button
                        type="button"
                        variant="destructive"
                        size="icon"
                        onClick={clearFile}
                        className="h-8 w-8"
                    >
                        <X className="h-4 w-4" />
                    </Button>
                )}
            </div>
            
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept={accept}
                className="hidden"
            />
            
            {preview && (
                <div className="relative mt-2 rounded-md border border-gray-200 p-2">
                    {accept.includes('image') ? (
                        <img
                            src={preview}
                            alt="Preview"
                            className="max-h-40 rounded-md object-contain"
                        />
                    ) : (
                        <div className="flex items-center gap-2 text-sm">
                            <div className="text-blue-500">File selected</div>
                        </div>
                    )}
                </div>
            )}
        </div>
    );
}
