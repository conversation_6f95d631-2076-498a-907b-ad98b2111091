<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('skill_settings', function (Blueprint $table) {
            $table->id();
            $table->string('skills_visualization_type')->default('radar');
            $table->string('skills_primary_color')->default('#3b82f6');
            $table->string('skills_secondary_color')->default('#8b5cf6');
            $table->boolean('skills_show_categories')->default(true);
            $table->boolean('skills_show_radar')->default(true);
            $table->boolean('skills_show_bars')->default(true);
            $table->integer('skills_max_display')->default(8);
            $table->text('skills_custom_css')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('skill_settings');
    }
};
