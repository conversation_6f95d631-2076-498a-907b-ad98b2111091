<?php

namespace App\Console\Commands;

use App\Models\HeroSection;
use Illuminate\Console\Command;

class CreateHeroSection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-hero-section';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a test hero section entry in the database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $heroSection = HeroSection::first();
        
        if ($heroSection) {
            $this->info('Hero section already exists!');
            return;
        }
        
        $heroSection = new HeroSection();
        $heroSection->title = 'Welcome to My Portfolio';
        $heroSection->subtitle = 'I build things for the web';
        $heroSection->model_settings = json_encode([
            'type' => 'cube',
            'size' => 2.5,
            'colors' => [
                'primary' => '#4f46e5',
                'secondary' => '#06b6d4',
                'tertiary' => '#8b5cf6'
            ],
            'rotation' => [
                'speed' => 0.5,
                'autoRotate' => true
            ]
        ]);
        $heroSection->background_settings = json_encode([
            'type' => 'particles',
            'particleCount' => 100,
            'particleColor' => '#6366f1',
            'particleSize' => 1.5,
            'particleSpeed' => 1.0
        ]);
        $heroSection->is_active = true;
        $heroSection->save();
        
        $this->info('Hero section created successfully!');
    }
}
