<?php

namespace App\Models;

use App\Helpers\FileHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class Setting extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'site_name',
        'site_description',
        'site_keywords',
        'site_logo',
        'site_favicon',
        'primary_color',
        'secondary_color',
        'google_analytics_id',
        'meta_image',
        'custom_css',
        'custom_js',
        'maintenance_mode'
    ];
    
    protected $casts = [
        'maintenance_mode' => 'boolean'
    ];
    
    /**
     * Get the site logo with proper URL formatting
     *
     * @param string|null $value
     * @return string
     */
    public function getSiteLogoAttribute($value)
    {
        if (empty($value)) {
            return '/images/logo.png';
        }
        
        try {
            return FileHelper::getPublicUrl($value, '/images/logo.png');
        } catch (\Exception $e) {
            Log::error('Error processing site logo URL: ' . $e->getMessage());
            return '/images/logo.png';
        }
    }
    
    /**
     * Get the site favicon with proper URL formatting
     *
     * @param string|null $value
     * @return string
     */
    public function getSiteFaviconAttribute($value)
    {
        if (empty($value)) {
            return '/images/favicon.ico';
        }
        
        try {
            return FileHelper::getPublicUrl($value, '/images/favicon.ico');
        } catch (\Exception $e) {
            Log::error('Error processing site favicon URL: ' . $e->getMessage());
            return '/images/favicon.ico';
        }
    }
    
    /**
     * Get the meta image with proper URL formatting
     *
     * @param string|null $value
     * @return string
     */
    public function getMetaImageAttribute($value)
    {
        if (empty($value)) {
            return '/images/meta.jpg';
        }
        
        try {
            return FileHelper::getPublicUrl($value, '/images/meta.jpg');
        } catch (\Exception $e) {
            Log::error('Error processing meta image URL: ' . $e->getMessage());
            return '/images/meta.jpg';
        }
    }
}
