<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\FooterSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class FooterSettingController extends Controller
{
    /**
     * Display the footer settings page.
     */
    public function index()
    {
        $footerSettings = FooterSetting::first() ?? new FooterSetting();
        
        return Inertia::render('admin/footer-settings', [
            'title' => 'Footer Settings',
            'footerSettings' => $footerSettings
        ]);
    }

    /**
     * Update the footer settings.
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'description' => 'nullable|string',
            'copyright_text' => 'nullable|string',
            'logo_size' => 'integer|min:50|max:200',
            'social_links' => 'nullable',
            'quick_links' => 'nullable',
            'contact_info' => 'nullable',
            'is_active' => 'boolean',
            'logo' => 'nullable|file|mimes:jpeg,png,jpg,gif,svg|max:2048',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        $footerSettings = FooterSetting::first();
        
        if (!$footerSettings) {
            $footerSettings = new FooterSetting();
        }
        
        // Handle logo upload
        if ($request->hasFile('logo')) {
            // Delete old logo if exists
            if ($footerSettings->logo_path) {
                Storage::delete($footerSettings->logo_path);
            }
            
            try {
                $file = $request->file('logo');
                $originalName = $file->getClientOriginalName();
                $extension = $file->getClientOriginalExtension();
                
                // Generate a unique filename
                $filename = 'footer_logo_' . time() . '.' . $extension;
                
                // Store the file
                $path = $file->storeAs('public/footer', $filename);
                $footerSettings->logo_path = $path;
            } catch (\Exception $e) {
                return redirect()->back()->with('error', 'Error uploading logo: ' . $e->getMessage());
            }
        }
        
        $footerSettings->description = $request->description;
        $footerSettings->copyright_text = $request->copyright_text;
        $footerSettings->logo_size = $request->logo_size;
        // Handle JSON data
        $footerSettings->social_links = json_decode($request->social_links, true);
        $footerSettings->quick_links = json_decode($request->quick_links, true);
        $footerSettings->contact_info = json_decode($request->contact_info, true);
        $footerSettings->is_active = $request->is_active;
        
        $footerSettings->save();
        
        return redirect()->back()->with('success', 'Footer settings updated successfully');
    }
}
