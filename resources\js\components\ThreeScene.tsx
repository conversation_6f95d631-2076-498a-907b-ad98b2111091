import { useEffect, useRef } from 'react';
import * as THREE from 'three';

interface ThreeSceneProps {
    modelSettings?: {
        type?: string;
        size?: number;
        colors?: {
            primary: string;
            secondary: string;
            tertiary: string;
        };
        rotation?: {
            speed: number;
            autoRotate: boolean;
        };
    };
}

export default function ThreeScene({ modelSettings: propModelSettings }: ThreeSceneProps) {
    const threeContainerRef = useRef<HTMLDivElement>(null);
    
    useEffect(() => {
        if (!threeContainerRef.current) return;
        
        // Parse model settings from props or use defaults
        let modelSettings = {
            type: 'cube',
            size: 2.5,
            colors: {
                primary: '#4f46e5',
                secondary: '#06b6d4',
                tertiary: '#8b5cf6'
            },
            rotation: {
                speed: 0.5,
                autoRotate: true
            }
        };
        
        // Override with props if available
        if (propModelSettings) {
            modelSettings = { ...modelSettings, ...propModelSettings };
        }
        
        // Create scene, camera, and renderer
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ 
            alpha: true, 
            antialias: true,
            powerPreference: 'high-performance' // Optimize for performance
        });
        
        // Use larger size for better visibility
        const containerWidth = threeContainerRef.current.clientWidth;
        const containerHeight = threeContainerRef.current.clientHeight;
        const canvasSize = Math.min(containerWidth, containerHeight) * 0.8; // Significantly increased size for better visibility
        
        renderer.setSize(canvasSize, canvasSize);
        renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)); // Limit pixel ratio for performance
        threeContainerRef.current.appendChild(renderer.domElement);
        
        // Position the renderer's canvas - make it more prominent
        renderer.domElement.style.position = 'absolute';
        renderer.domElement.style.right = '10%';
        renderer.domElement.style.top = '50%';
        renderer.domElement.style.transform = 'translateY(-50%)';
        renderer.domElement.style.zIndex = '15';
        renderer.domElement.style.boxShadow = '0 0 30px rgba(79, 70, 229, 0.5)';
        renderer.domElement.style.borderRadius = '50%';
        renderer.domElement.style.background = 'transparent';
        
        // Position camera
        camera.position.z = 30;
        
        // Create a simplified cube for better performance
        const size = modelSettings.size || 2.5; 
        const geometry = new THREE.BoxGeometry(size, size, size);
        
        // Use a more vibrant material for better visibility
        const material = new THREE.MeshPhongMaterial({
            color: modelSettings.colors?.primary || '#4f46e5',
            specular: modelSettings.colors?.secondary || '#06b6d4',
            shininess: 100,
            transparent: true,
            opacity: 1.0,
            emissive: new THREE.Color(modelSettings.colors?.tertiary || '#8b5cf6'),
            emissiveIntensity: 0.5
        });
        
        // Create the cube mesh
        const cube = new THREE.Mesh(geometry, material);
        
        // Position the cube
        cube.position.set(-4, 0, 0);
        
        // Add enhanced lighting to make the model more visible
        const ambientLight = new THREE.AmbientLight(0x404040, 1.5);
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1.5);
        directionalLight.position.set(1, 1, 1);
        
        // Add point lights for extra highlights
        const pointLight1 = new THREE.PointLight(0x4f46e5, 1, 100);
        pointLight1.position.set(10, 10, 10);
        
        const pointLight2 = new THREE.PointLight(0x06b6d4, 1, 100);
        pointLight2.position.set(-10, -10, 10);
        
        // Add to scene
        scene.add(cube);
        scene.add(ambientLight);
        scene.add(directionalLight);
        scene.add(pointLight1);
        scene.add(pointLight2);
        
        // Handle resize
        const handleResize = () => {
            if (!threeContainerRef.current) return;
            
            const containerWidth = threeContainerRef.current.clientWidth;
            const containerHeight = threeContainerRef.current.clientHeight;
            const canvasSize = Math.min(containerWidth, containerHeight) * 0.8; // Maintain larger size on resize
            renderer.setSize(canvasSize, canvasSize);
        };
        
        window.addEventListener('resize', handleResize);
        
        // Animation loop with throttling for better performance
        let lastTime = 0;
        const animate = (time: number) => {
            requestAnimationFrame(animate);
            
            // Throttle updates to 30fps for better performance
            if (time - lastTime < 33) { // ~30fps
                return;
            }
            lastTime = time;
            
            // Rotate the cube
            const rotationSpeed = modelSettings?.rotation?.speed || 0.5;
            cube.rotation.x += 0.009 * rotationSpeed;
            cube.rotation.y += 0.007 * rotationSpeed;
            
            // Render the scene
            renderer.render(scene, camera);
        };
        
        // Start the animation loop
        animate(0);
        
        // Clean up on unmount
        return () => {
            window.removeEventListener('resize', handleResize);
            if (threeContainerRef.current && threeContainerRef.current.contains(renderer.domElement)) {
                threeContainerRef.current.removeChild(renderer.domElement);
            }
            
            // Dispose of resources
            geometry.dispose();
            material.dispose();
            renderer.dispose();
        };
    }, [propModelSettings]);
    
    return <div ref={threeContainerRef} className="three-container" style={{ position: 'relative', height: '100%' }} />;
}
