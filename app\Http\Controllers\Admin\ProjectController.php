<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProjectSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $projects = ProjectSection::orderBy('order')->get();
        
        // Debug information
        \Log::info('Projects Data:', ['count' => $projects->count()]);
        
        return Inertia::render('admin/projects', [
            'title' => 'Projects',
            'projects' => $projects
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Log the request data for debugging
        \Log::info('Project Store Request: ' . json_encode(['data' => $request->all()]));
        
        // Validate the request
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image_path' => 'nullable|string',
            'github_url' => 'nullable|string|url',
            'live_url' => 'nullable|string|url',
            'technologies' => 'required|string',
            'order' => 'nullable|integer',
            'is_featured' => 'required',
            'is_active' => 'required'
        ]);

        if ($validator->fails()) {
            \Log::error('Validation failed: ' . json_encode(['errors' => $validator->errors()]));
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            // Create the project
            $project = new ProjectSection();
            $project->title = $request->title;
            
            // Ensure description is never empty
            if (empty($request->description)) {
                \Log::warning('Empty description received, using default value');
                $project->description = 'Project description';
            } else {
                $project->description = $request->description;
            }
            
            $project->image_path = $request->image_path ?? '';
            $project->github_url = $request->github_url ?? '';
            $project->live_url = $request->live_url ?? '';
            $project->technologies = $request->technologies;
            $project->order = $request->order ?? 0;
            $project->is_featured = $request->is_featured ? true : false;
            $project->is_active = $request->is_active ? true : false;
            $project->save();

            // Log success
            
            \Log::info('Project created successfully', ['id' => $project->id]);
            return redirect()->back()->with('success', 'Project created successfully');
        } catch (\Exception $e) {
            \Log::error('Error creating project:', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return redirect()->back()->withErrors(['error' => 'Failed to create project: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $project = ProjectSection::findOrFail($id);
        return response()->json($project);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        // Log the request data for debugging
        \Log::info('Project Update Request: ' . json_encode(['data' => $request->all()]));
        
        // Validate the request
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'image_path' => 'nullable|string',
            'github_url' => 'nullable|string|url',
            'live_url' => 'nullable|string|url',
            'technologies' => 'required|string',
            'order' => 'nullable|integer',
            'is_featured' => 'required',
            'is_active' => 'required'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed: ' . json_encode(['errors' => $validator->errors()]));
            return response()->json(['errors' => $validator->errors()], 422);
        }
        
        try {
            // Find the project
            $project = ProjectSection::findOrFail($id);
            
            // Update the project
            $project->title = $request->title;
            
            // Ensure description is never empty
            if (empty($request->description)) {
                \Log::warning('Empty description received in update, using default value');
                $project->description = 'Project description';
            } else {
                $project->description = $request->description;
            }
            
            $project->image_path = $request->image_path ?? '';
            $project->github_url = $request->github_url ?? '';
            $project->live_url = $request->live_url ?? '';
            $project->technologies = $request->technologies;
            $project->order = $request->order ?? $project->order;
            $project->is_featured = $request->is_featured ? true : false;
            $project->is_active = $request->is_active ? true : false;
            
            $project->save();
            
            // Log success
            \Log::info('Project updated successfully', ['id' => $project->id]);
            return redirect()->back()->with('success', 'Project updated successfully');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Error updating project: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error updating project: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $project = ProjectSection::findOrFail($id);
            $project->delete();
            
            \Log::info('Project deleted successfully', ['id' => $id]);
            return redirect()->back()->with('success', 'Project deleted successfully');
        } catch (\Exception $e) {
            \Log::error('Error deleting project:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to delete project: ' . $e->getMessage()]);
        }
    }
}
