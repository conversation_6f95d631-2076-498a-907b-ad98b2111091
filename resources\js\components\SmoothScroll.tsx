import React, { useEffect } from 'react';
import Lenis from '@studio-freight/lenis';

interface SmoothScrollProps {
    options?: {
        duration?: number;
        easing?: (t: number) => number;
        wheelMultiplier?: number;
        smoothWheel?: boolean;
    };
}

export default function SmoothScroll({ 
    options = {
        duration: 1.2,
        easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)),
        wheelMultiplier: 1,
        smoothWheel: true,
    } 
}: SmoothScrollProps) {
    useEffect(() => {
        console.log('Initializing smooth scroll');
        
        // Create Lenis instance with type assertion to allow smoothTouch property
        const lenis = new Lenis({
            duration: options.duration,
            easing: options.easing,
            wheelMultiplier: options.wheelMultiplier,
            smoothWheel: options.smoothWheel,
            // @ts-ignore - smoothTouch is available in newer versions but not in type definitions
            smoothTouch: false, // Disable on touch for better performance
        });
        
        // Use a more efficient RAF loop
        let rafId: number;
        
        function raf(time: number) {
            lenis.raf(time);
            rafId = requestAnimationFrame(raf);
        }
        
        requestAnimationFrame(raf);
        
        // Handle anchor links with smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href') || '');
                if (target) {
                    lenis.scrollTo(target as HTMLElement);
                }
            });
        });
        
        return () => {
            // Clean up
            cancelAnimationFrame(rafId);
            lenis.destroy();
        };
    }, [options.duration, options.easing, options.wheelMultiplier, options.smoothWheel]);

    // This is a utility component that doesn't render anything
    return null;
}
