<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ContactSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class ContactController extends Controller
{
    /**
     * Display the contact section data
     */
    public function index()
    {
        $contactSection = ContactSection::first() ?? new ContactSection([
            'email' => '<EMAIL>',
            'phone' => '+****************',
            'address' => '123 Main St, City, Country',
            'social_links' => [
                'github' => 'https://github.com/johndoe',
                'linkedin' => 'https://linkedin.com/in/johndoe',
                'twitter' => 'https://twitter.com/johndoe'
            ],
            'show_contact_form' => true,
            'form_recipient_email' => '<EMAIL>',
            'is_active' => true
        ]);
        
        return Inertia::render('admin/contact', [
            'title' => 'Contact Information',
            'contactData' => $contactSection
        ]);
    }
    
    /**
     * Update the contact section data
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:255',
            'social_links' => 'nullable|array',
            'show_contact_form' => 'boolean',
            'form_recipient_email' => 'nullable|email',
            'is_active' => 'boolean'
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        $contactSection = ContactSection::first();
        
        if (!$contactSection) {
            $contactSection = new ContactSection();
        }
        
        $contactSection->fill($request->all());
        $contactSection->save();
        
        return redirect()->back()->with('success', 'Contact information updated successfully');
    }

    // No other methods needed for the contact section
}
