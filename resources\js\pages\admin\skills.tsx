import { useState, useEffect } from 'react';
import { router, usePage } from '@inertiajs/react';
import { Plus, Pencil, Trash2, GripVertical, Save } from 'lucide-react';

import AdminLayout from './layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
    Dialog, 
    DialogContent, 
    DialogDescription, 
    DialogFooter, 
    DialogHeader, 
    DialogTitle, 
    DialogTrigger 
} from '@/components/ui/dialog';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Al<PERSON>, AlertDescription } from '@/components/ui/alert';

// Skill type definition
interface Skill {
    id: number;
    name: string;
    proficiency: number;
    category: string;
    is_featured: boolean;
    is_active: boolean;
    icon: string;
    order: number;
    // Additional properties for UI interaction
    level?: number; // Maps to proficiency for UI
    featured?: boolean; // Maps to is_featured for UI
    [key: string]: any; // Index signature for router compatibility
}

// Interface for the page props
interface SkillsPageProps {
    skills: Skill[];
    title: string;
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: any; // Add index signature to satisfy PageProps constraint
}

// Skill categories
const technicalCategories = ['Frontend', 'Backend', 'Database', '3D', 'DevOps', 'Mobile'];
const designCategories = ['Design', '3D', 'Software', 'Animation', 'Illustration'];

export default function SkillsAdmin() {
    const { skills: initialSkills, flash } = usePage<SkillsPageProps>().props;
    const [skills, setSkills] = useState<Skill[]>(initialSkills || []);
    const [activeTab, setActiveTab] = useState<'technical' | 'design'>('technical');
    
    // Type-safe wrapper for setActiveTab to satisfy the Tabs component's onValueChange prop
    const handleTabChange = (value: string) => {
        if (value === 'technical' || value === 'design') {
            setActiveTab(value);
        }
    };
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [currentSkill, setCurrentSkill] = useState<Skill | null>(null);
    const [saveSuccess, setSaveSuccess] = useState(false);
    const [newSkill, setNewSkill] = useState<Omit<Skill, 'id'>>({
        name: '',
        proficiency: 70,
        category: '',
        is_featured: false,
        is_active: true,
        icon: '',
        order: 0,
        level: 70,
        featured: false
    });

    // Handle adding a new skill
    const handleAddSkill = () => {
        // Send the new skill to the server
        router.post('/admin/skills', newSkill as Record<string, any>, {
            onSuccess: () => {
                setIsAddDialogOpen(false);
                setNewSkill({
                    name: '',
                    proficiency: 70,
                    category: '',
                    is_featured: false,
                    is_active: true,
                    icon: '',
                    order: 0,
                    level: 70, // Add level property
                    featured: false // Add featured property
                });
            }
        });
    };

    // Handle editing a skill
    const handleEditSkill = () => {
        if (!currentSkill) return;
        
        // Send the updated skill to the server
        router.put(`/admin/skills/${currentSkill.id}`, currentSkill as Record<string, any>, {
            onSuccess: () => {
                setIsEditDialogOpen(false);
            }
        });
    };

    // Handle deleting a skill
    const handleDeleteSkill = () => {
        if (!currentSkill) return;
        
        // Send the delete request to the server
        router.delete(`/admin/skills/${currentSkill.id}`, {
            onSuccess: () => {
                setIsDeleteDialogOpen(false);
            }
        });
    };

    // Save all changes
    const handleSaveChanges = () => {
        // This is now handled by the individual add/edit/delete operations
        setSaveSuccess(true);
        
        // Hide success message after 3 seconds
        setTimeout(() => {
            setSaveSuccess(false);
        }, 3000);
    };
    
    // Display flash messages from the server
    useEffect(() => {
        if (flash?.success) {
            setSaveSuccess(true);
            
            // Hide success message after 3 seconds
            setTimeout(() => {
                setSaveSuccess(false);
            }, 3000);
        }
    }, [flash]);

    return (
        <AdminLayout title="Skills" currentPage="skills">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Skills</h1>
                    <p className="text-gray-500 dark:text-gray-400">Manage your technical and design skills</p>
                </div>
                <div className="flex gap-2">
                    <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                        <DialogTrigger asChild>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                Add Skill
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-[425px]">
                            <DialogHeader>
                                <DialogTitle>Add New Skill</DialogTitle>
                                <DialogDescription>
                                    Add a new {activeTab} skill to your portfolio.
                                </DialogDescription>
                            </DialogHeader>
                            <div className="grid gap-4 py-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="name">Skill Name</Label>
                                    <Input 
                                        id="name" 
                                        value={newSkill.name} 
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewSkill({...newSkill, name: e.target.value})}
                                        placeholder="Enter skill name"
                                    />
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="category">Category</Label>
                                    <Select 
                                        value={newSkill.category} 
                                        onValueChange={(value) => setNewSkill({...newSkill, category: value})}
                                    >
                                        <SelectTrigger id="category">
                                            <SelectValue placeholder="Select category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {(activeTab === 'technical' ? technicalCategories : designCategories).map((category) => (
                                                <SelectItem key={category} value={category}>{category}</SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="level">Proficiency Level ({newSkill.level}%)</Label>
                                    <Input 
                                        id="level" 
                                        type="range"
                                        min="10"
                                        max="100"
                                        step="5"
                                        value={newSkill.level} 
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewSkill({...newSkill, level: parseInt(e.target.value), proficiency: parseInt(e.target.value)})}
                                    />
                                </div>
                                <div className="grid gap-2">
                                    <Label htmlFor="featured">Featured</Label>
                                    <Select 
                                        value={newSkill.featured ? "true" : "false"} 
                                        onValueChange={(value) => setNewSkill({...newSkill, featured: value === "true", is_featured: value === "true"})}
                                    >
                                        <SelectTrigger id="featured">
                                            <SelectValue placeholder="Featured?" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="true">Yes</SelectItem>
                                            <SelectItem value="false">No</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                            <DialogFooter>
                                <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>Cancel</Button>
                                <Button onClick={handleAddSkill}>Add Skill</Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                    <Button variant="outline" onClick={handleSaveChanges}>
                        <Save className="mr-2 h-4 w-4" />
                        Save Changes
                    </Button>
                </div>
            </div>

            {/* Success Alert */}
            {saveSuccess && (
                <Alert className="mb-6 bg-green-50 text-green-800 dark:bg-green-900/30 dark:text-green-400">
                    <AlertDescription>
                        Skills updated successfully!
                    </AlertDescription>
                </Alert>
            )}

            {/* Skills Tabs */}
            <Tabs defaultValue="technical" value={activeTab} onValueChange={handleTabChange} className="mb-6">
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="technical">Technical Skills</TabsTrigger>
                    <TabsTrigger value="design">Design Skills</TabsTrigger>
                </TabsList>
                
                {/* Technical Skills Tab */}
                <TabsContent value="technical">
                    <Card>
                        <CardHeader>
                            <CardTitle>Technical Skills</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {skills.filter(skill => skill.category === 'development' || skill.category === 'Frontend' || skill.category === 'Backend').map((skill) => (
                                    <SkillItem 
                                        key={skill.id} 
                                        skill={skill} 
                                        onEdit={() => {
                                            setCurrentSkill(skill);
                                            setIsEditDialogOpen(true);
                                        }}
                                        onDelete={() => {
                                            setCurrentSkill(skill);
                                            setIsDeleteDialogOpen(true);
                                        }}
                                    />
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
                
                {/* Design Skills Tab */}
                <TabsContent value="design">
                    <Card>
                        <CardHeader>
                            <CardTitle>Design Skills</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {skills.filter(skill => skill.category === 'design' || skill.category === '3D' || skill.category === 'Design').map((skill) => (
                                    <SkillItem 
                                        key={skill.id} 
                                        skill={skill} 
                                        onEdit={() => {
                                            setCurrentSkill(skill);
                                            setIsEditDialogOpen(true);
                                        }}
                                        onDelete={() => {
                                            setCurrentSkill(skill);
                                            setIsDeleteDialogOpen(true);
                                        }}
                                    />
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>

            {/* Edit Skill Dialog */}
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Edit Skill</DialogTitle>
                        <DialogDescription>
                            Make changes to the skill details.
                        </DialogDescription>
                    </DialogHeader>
                    {currentSkill && (
                        <div className="grid gap-4 py-4">
                            <div className="grid gap-2">
                                <Label htmlFor="edit-name">Skill Name</Label>
                                <Input 
                                    id="edit-name" 
                                    value={currentSkill?.name || ''} 
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => currentSkill && setCurrentSkill({...currentSkill, name: e.target.value})}
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-category">Category</Label>
                                <Select 
                                    value={currentSkill?.category || ''} 
                                    onValueChange={(value) => currentSkill && setCurrentSkill({...currentSkill, category: value})}
                                >
                                    <SelectTrigger id="edit-category">
                                        <SelectValue placeholder="Select category" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {(activeTab === 'technical' ? technicalCategories : designCategories).map((category) => (
                                            <SelectItem key={category} value={category}>{category}</SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-level">Proficiency Level ({currentSkill?.proficiency || 0}%)</Label>
                                <Input 
                                    id="edit-level" 
                                    type="range"
                                    min="10"
                                    max="100"
                                    step="5"
                                    value={currentSkill?.proficiency.toString() || '0'} 
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => currentSkill && setCurrentSkill({...currentSkill, proficiency: parseInt(e.target.value)})}
                                />
                            </div>
                            <div className="grid gap-2">
                                <Label htmlFor="edit-featured">Featured</Label>
                                <Select 
                                    value={currentSkill?.is_featured ? "true" : "false"} 
                                    onValueChange={(value) => currentSkill && setCurrentSkill({...currentSkill, is_featured: value === "true"})}
                                >
                                    <SelectTrigger id="edit-featured">
                                        <SelectValue placeholder="Featured?" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="true">Yes</SelectItem>
                                        <SelectItem value="false">No</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    )}
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
                        <Button onClick={handleEditSkill}>Save Changes</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>

            {/* Delete Confirmation Dialog */}
            <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <DialogContent className="sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>Delete Skill</DialogTitle>
                        <DialogDescription>
                            Are you sure you want to delete this skill? This action cannot be undone.
                        </DialogDescription>
                    </DialogHeader>
                    {currentSkill && (
                        <div className="py-4">
                            <p className="font-medium text-gray-900 dark:text-white">{currentSkill?.name}</p>
                            <p className="text-sm text-gray-500 dark:text-gray-400">{currentSkill?.category}</p>
                        </div>
                    )}
                    <DialogFooter>
                        <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
                        <Button variant="destructive" onClick={handleDeleteSkill}>Delete Skill</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}

// Skill Item Component
interface SkillItemProps {
    skill: {
        id: number;
        name: string;
        proficiency: number;
        category: string;
        is_featured: boolean;
        is_active: boolean;
        icon: string;
        order: number;
    };
    onEdit: () => void;
    onDelete: () => void;
}

function SkillItem({ skill, onEdit, onDelete }: SkillItemProps) {
    return (
        <div className="flex items-center space-x-4">
            <div className="flex-none">
                <Button variant="ghost" size="icon" className="cursor-move">
                    <GripVertical className="h-4 w-4 text-gray-400" />
                </Button>
            </div>
            <div className="flex-1">
                <div className="flex items-center">
                    <h4 className="font-medium text-gray-900 dark:text-white">{skill.name}</h4>
                    {skill.is_featured && (
                        <Badge variant="secondary" className="ml-2 bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400">
                            Featured
                        </Badge>
                    )}
                    <Badge variant="outline" className="ml-2">
                        {skill.category}
                    </Badge>
                </div>
                <div className="mt-2 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                    <div 
                        className="bg-blue-600 h-2.5 rounded-full" 
                        style={{ width: `${skill.proficiency}%` }}
                    ></div>
                </div>
                <div className="mt-1 flex justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>Beginner</span>
                    <span>Advanced</span>
                </div>
            </div>
            <div className="flex-none flex space-x-1">
                <Button variant="ghost" size="icon" onClick={onEdit}>
                    <Pencil className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" onClick={onDelete}>
                    <Trash2 className="h-4 w-4 text-red-500" />
                </Button>
            </div>
        </div>
    );
}
