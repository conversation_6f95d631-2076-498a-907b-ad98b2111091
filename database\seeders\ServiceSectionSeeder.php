<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ServiceSectionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing services
        \App\Models\ServiceSection::truncate();
        
        // Add sample services with different icon types
        $services = [
            [
                'title' => 'Web Development',
                'description' => 'Custom web applications and responsive websites using modern frameworks and technologies.',
                'icon' => 'code',
                'order' => 1,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'title' => 'Mobile App Development',
                'description' => 'Native and cross-platform mobile applications for iOS and Android.',
                'icon' => 'fa-mobile',
                'order' => 2,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'title' => 'UI/UX Design',
                'description' => 'User-centered design solutions that enhance user experience and engagement.',
                'icon' => 'palette',
                'order' => 3,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'title' => 'E-commerce Solutions',
                'description' => 'Complete e-commerce platforms with secure payment gateways and inventory management.',
                'icon' => 'fa-shopping-cart',
                'order' => 4,
                'is_featured' => false,
                'is_active' => true,
            ],
            [
                'title' => 'Cloud Services',
                'description' => 'Cloud infrastructure setup, migration, and management services.',
                'icon' => 'fa-cloud',
                'order' => 5,
                'is_featured' => false,
                'is_active' => true,
            ],
            [
                'title' => 'Digital Marketing',
                'description' => 'SEO, social media marketing, and content strategy to boost your online presence.',
                'icon' => '🚀',
                'order' => 6,
                'is_featured' => false,
                'is_active' => true,
            ],
        ];
        
        foreach ($services as $service) {
            \App\Models\ServiceSection::create($service);
        }
    }
}
