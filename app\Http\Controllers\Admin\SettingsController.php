<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SettingsController extends Controller
{
    /**
     * Display the settings page
     */
    public function index()
    {
        $settings = Setting::first() ?? new Setting([
            'site_name' => 'My Portfolio',
            'site_description' => 'My personal portfolio website',
            'site_keywords' => 'portfolio, web developer, designer',
            'site_logo' => '/img/logo.png',
            'site_favicon' => '/img/favicon.ico',
            'primary_color' => '#3b82f6',
            'secondary_color' => '#10b981',
            'google_analytics_id' => '',
            'meta_image' => '/img/meta-image.jpg',
            'custom_css' => '',
            'custom_js' => '',
            'maintenance_mode' => false
        ]);
        
        // Process image URLs to ensure they're properly formatted
        $settings->site_logo = FileHelper::getPublicUrl($settings->site_logo, '/img/logo.png');
        $settings->site_favicon = FileHelper::getPublicUrl($settings->site_favicon, '/img/favicon.ico');
        $settings->meta_image = FileHelper::getPublicUrl($settings->meta_image, '/img/meta-image.jpg');
        
        return Inertia::render('admin/settings', [
            'title' => 'Settings',
            'settings' => $settings
        ]);
    }

    /**
     * Update the settings
     */
    public function update(Request $request)
    {
        // Debug request data
        \Log::info('Settings Update Request:', ['data' => $request->all()]);
        
        $validator = Validator::make($request->all(), [
            'site_name' => 'required|string|max:255',
            'site_description' => 'nullable|string',
            'site_keywords' => 'nullable|string',
            'site_logo' => 'nullable|string',
            'site_favicon' => 'nullable|string',
            'primary_color' => 'nullable|string',
            'secondary_color' => 'nullable|string',
            'google_analytics_id' => 'nullable|string',
            'meta_image' => 'nullable|string',
            'custom_css' => 'nullable|string',
            'custom_js' => 'nullable|string',
            'maintenance_mode' => 'boolean'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed:', ['errors' => $validator->errors()->toArray()]);
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        try {
            $settings = Setting::first();
            
            if (!$settings) {
                $settings = new Setting();
            }
            
            $settings->site_name = $request->site_name;
            $settings->site_description = $request->site_description;
            $settings->site_keywords = $request->site_keywords;
            $settings->site_logo = $request->site_logo;
            $settings->site_favicon = $request->site_favicon;
            $settings->primary_color = $request->primary_color;
            $settings->secondary_color = $request->secondary_color;
            $settings->google_analytics_id = $request->google_analytics_id;
            $settings->meta_image = $request->meta_image;
            $settings->custom_css = $request->custom_css;
            $settings->custom_js = $request->custom_js;
            $settings->maintenance_mode = $request->maintenance_mode ?? false;
            
            $settings->save();
            
            \Log::info('Settings updated successfully');
            return redirect()->back()->with('success', 'Settings updated successfully');
        } catch (\Exception $e) {
            \Log::error('Error updating settings:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to update settings: ' . $e->getMessage()])->withInput();
        }
    }
}
