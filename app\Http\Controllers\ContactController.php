<?php

namespace App\Http\Controllers;

use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use App\Mail\ContactFormSubmission;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
use Inertia\Inertia;

class ContactController extends Controller
{
    /**
     * Generate a CAPTCHA value and store it in the session
     */
    public function generateCaptcha()
    {
        // Generate a simple math problem (e.g., 3 + 4)
        $num1 = rand(1, 9);
        $num2 = rand(1, 9);
        $captchaValue = $num1 + $num2;
        
        // Store the CAPTCHA value in the session
        Session::put('captcha_value', $captchaValue);
        
        return [
            'num1' => $num1,
            'num2' => $num2
        ];
    }

    /**
     * Handle the contact form submission
     */
    public function submit(Request $request)
    {
        // Validate the form data
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
            'captcha' => 'required|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        // Verify CAPTCHA using the session value
        $captchaValue = Session::get('captcha_value', 8); // Default to 8 if no session value exists
        
        if ((int)$request->captcha !== $captchaValue) {
            return redirect()->back()
                ->withErrors(['captcha' => 'The security check answer is incorrect.'])
                ->withInput();
        }

        try {
            // Store the message in the database
            $message = new Message();
            $message->name = $request->name;
            $message->email = $request->email;
            $message->subject = $request->subject;
            $message->message = $request->message;
            $message->status = 'unread';
            $message->save();

            // Send email notification
            Mail::to(env('MAIL_FROM_ADDRESS', '<EMAIL>'))->send(
                new ContactFormSubmission($request->all())
            );

            // Generate a new CAPTCHA for the next submission
            $this->generateCaptcha();
            
            // Return success response
            return redirect()->back()->with('success', 'Your message has been sent successfully!');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Contact form error: ' . $e->getMessage());
            
            // Return error response
            return redirect()->back()->with('error', 'Sorry, there was an error sending your message. Please try again later.');
        }
    }
}
