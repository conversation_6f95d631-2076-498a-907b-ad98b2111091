<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\HeroSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class HeroController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $heroSection = HeroSection::first();
        
        if (!$heroSection) {
            // Create a default hero section if none exists
            $heroSection = new HeroSection([
                'title' => 'Welcome to My Portfolio',
                'subtitle' => 'I build things for the web',
                'model_settings' => json_encode([
                    'type' => 'cube',
                    'size' => 2.5,
                    'colors' => [
                        'primary' => '#4f46e5',
                        'secondary' => '#06b6d4',
                        'tertiary' => '#8b5cf6'
                    ],
                    'rotation' => [
                        'speed' => 0.5,
                        'autoRotate' => true
                    ],
                    'position' => ['x' => 0, 'y' => 0, 'z' => 0],
                    'wireframe' => false,
                    'glow' => true,
                    'particles' => true
                ]),
                'background_settings' => json_encode([
                    'type' => 'particles',
                    'particleCount' => 100,
                    'particleColor' => '#6366f1',
                    'particleSize' => 1.5,
                    'particleSpeed' => 1.0
                ]),
                'is_active' => true
            ]);
            $heroSection->save();
        }
        
        // Debug information
        \Log::info('Hero Section Data:', ['data' => $heroSection->toArray()]);
        
        return Inertia::render('admin/hero', [
            'heroData' => $heroSection
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        // Debug request data
        \Log::info('Hero Update Request:', ['data' => $request->all()]);
        
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'subtitle' => 'required|string|max:255',
            'model_settings' => 'required|string',  // Changed from array to string
            'background_settings' => 'required|string',  // Changed from array to string
            'is_active' => 'boolean'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed:', ['errors' => $validator->errors()->toArray()]);
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        $heroSection = HeroSection::first();
        
        if (!$heroSection) {
            $heroSection = new HeroSection();
        }
        
        $heroSection->title = $request->title;
        $heroSection->subtitle = $request->subtitle;
        $heroSection->model_settings = $request->model_settings; // Already a JSON string from frontend
        $heroSection->background_settings = $request->background_settings; // Already a JSON string from frontend
        $heroSection->is_active = $request->is_active ?? true;
        
        try {
            $heroSection->save();
            \Log::info('Hero section saved successfully', ['id' => $heroSection->id]);
            return redirect()->back()->with('success', 'Hero section updated successfully');
        } catch (\Exception $e) {
            \Log::error('Error saving hero section:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to save hero section: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
