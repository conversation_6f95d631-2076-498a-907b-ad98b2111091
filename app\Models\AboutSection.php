<?php

namespace App\Models;

use App\Helpers\FileHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class AboutSection extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'name',
        'title',
        'subtitle',
        'bio',
        'content',
        'profile_image',
        'resume_link',
        'education',
        'experience',
        'social_links',
        'is_active'
    ];
    
    protected $casts = [
        'education' => 'json',
        'experience' => 'json',
        'social_links' => 'json',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get the profile image with proper URL formatting
     *
     * @param string|null $value
     * @return string
     */
    public function getProfileImageAttribute($value)
    {
        if (empty($value)) {
            return '/images/profile.jpg';
        }
        
        try {
            return FileHelper::getPublicUrl($value, '/images/profile.jpg');
        } catch (\Exception $e) {
            Log::error('Error processing profile image URL: ' . $e->getMessage());
            return '/images/profile.jpg';
        }
    }
}
