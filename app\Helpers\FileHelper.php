<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\URL;

class FileHelper
{
    /**
     * Get the correct public URL for a file path
     * This handles both absolute URLs and relative paths
     *
     * @param string|null $path
     * @param string $default
     * @return string
     */
    public static function getPublicUrl(?string $path, string $default = ''): string
    {
        if (empty($path)) {
            return $default;
        }

        // If it's already a full URL, return it as is
        if (filter_var($path, FILTER_VALIDATE_URL)) {
            return $path;
        }
        
        // If it already starts with /storage, just ensure it has the correct URL format
        if (str_starts_with($path, '/storage')) {
            return URL::to($path);
        }
        
        // Handle paths that start with storage/ (common in Laravel)
        if (str_starts_with($path, 'storage/')) {
            return URL::to('/' . $path);
        }
        
        // If it starts with public/, convert it to a storage URL
        if (str_starts_with($path, 'public/')) {
            $relativePath = str_replace('public/', '', $path);
            return URL::to('/storage/' . $relativePath);
        }
        
        // If it's a storage path from Laravel's Storage facade
        if (str_starts_with($path, 'app/public/')) {
            $relativePath = str_replace('app/public/', '', $path);
            return URL::to('/storage/' . $relativePath);
        }
        
        // If it starts with storage/app/public, convert it to a public URL
        if (str_starts_with($path, 'storage/app/public/')) {
            $relativePath = str_replace('storage/app/public/', '', $path);
            return URL::to('/storage/' . $relativePath);
        }
        
        // For paths that don't match any of the above patterns
        // First check if it's a file in the public directory
        $cleanPath = ltrim($path, '/');
        if (file_exists(public_path($cleanPath))) {
            return URL::to('/' . $cleanPath);
        }
        
        // If it might be a storage path without the proper prefix
        if (file_exists(storage_path('app/public/' . $cleanPath))) {
            return URL::to('/storage/' . $cleanPath);
        }
        
        // If all else fails, return the path with a leading slash
        return '/' . $cleanPath;
    }
}
