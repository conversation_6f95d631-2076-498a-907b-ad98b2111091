<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadController extends Controller
{
    /**
     * Handle file upload
     */
    public function upload(Request $request)
    {
        try {
            // Validate the uploaded file
            $request->validate([
                'file' => 'required|file|max:10240', // 10MB max
            ]);

            if (!$request->hasFile('file')) {
                return response()->json([
                    'success' => false,
                    'message' => 'No file uploaded'
                ], 400);
            }

            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $extension = $file->getClientOriginalExtension();
            
            // Generate a unique filename
            $filename = Str::slug(pathinfo($originalName, PATHINFO_FILENAME)) . '_' . time() . '.' . $extension;
            
            // Determine the storage path based on file type
            $path = 'public/uploads';
            if (in_array(strtolower($extension), ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'])) {
                $path = 'public/uploads/images';
            } elseif (in_array(strtolower($extension), ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
                $path = 'public/uploads/documents';
            } elseif (in_array(strtolower($extension), ['ico', 'icon'])) {
                $path = 'public/uploads/icons';
            }
            
            // Make sure the directory exists
            $directory = storage_path('app/' . $path);
            if (!file_exists($directory)) {
                mkdir($directory, 0755, true);
            }
            
            // Store the file using put method to ensure it's written to disk
            $fullPath = $directory . '/' . $filename;
            $fileContents = file_get_contents($file->getRealPath());
            
            if (file_put_contents($fullPath, $fileContents) === false) {
                throw new \Exception('Failed to write file to disk');
            }
            
            // Also store using Laravel's Storage facade for consistency
            $filePath = Storage::putFileAs($path, $file, $filename);
            
            if (!$filePath) {
                throw new \Exception('Failed to store file using Storage facade');
            }
            
            // Generate the correct public URL
            $relativePath = str_replace('public/', '', $filePath);
            $publicPath = '/storage/' . $relativePath;
            
            // Ensure the file is accessible in the public directory
            $publicFilePath = public_path('storage/' . $relativePath);
            $publicDirectory = dirname($publicFilePath);
            
            if (!file_exists($publicDirectory)) {
                mkdir($publicDirectory, 0755, true);
            }
            
            // If the file doesn't exist in the public directory, copy it there
            if (!file_exists($publicFilePath)) {
                copy($fullPath, $publicFilePath);
            }
            
            Log::info('File uploaded successfully', [
                'original_name' => $originalName,
                'stored_path' => $filePath,
                'public_path' => $publicPath,
                'full_path' => $fullPath,
                'public_file_path' => $publicFilePath
            ]);
            
            return response()->json([
                'success' => true,
                'path' => $publicPath,
                'filename' => $filename
            ]);
            
        } catch (\Exception $e) {
            Log::error('File upload error: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ], 500);
        }
    }
}
