<?php

namespace App\Console\Commands;

use App\Models\AboutSection;
use App\Models\ContactSection;
use App\Models\ProjectSection;
use App\Models\ServiceSection;
use App\Models\SkillSection;
use Illuminate\Console\Command;

class CreateInitialData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-initial-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create initial test data for all front-page sections';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->createProjects();
        $this->createSkills();
        $this->createAbout();
        $this->createServices();
        $this->createContact();
        
        $this->info('All initial data created successfully!');
    }
    
    /**
     * Create test project data
     */
    private function createProjects()
    {
        if (ProjectSection::count() > 0) {
            $this->info('Projects already exist!');
            return;
        }
        
        $projects = [
            [
                'title' => '3D Product Visualization',
                'description' => 'High-quality 3D product visualization for marketing materials',
                'image_path' => '/images/projects/project1.jpg',
                'github_url' => 'https://github.com/username/project1',
                'live_url' => 'https://project1.example.com',
                'technologies' => json_encode(['Blender', 'Three.js', 'WebGL']),
                'order' => 1,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'title' => 'E-commerce Website',
                'description' => 'Modern e-commerce website with payment integration',
                'image_path' => '/images/projects/project2.jpg',
                'github_url' => 'https://github.com/username/project2',
                'live_url' => 'https://project2.example.com',
                'technologies' => json_encode(['Laravel', 'React', 'Stripe']),
                'order' => 2,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'title' => 'Mobile App UI Design',
                'description' => 'UI/UX design for a fitness tracking mobile app',
                'image_path' => '/images/projects/project3.jpg',
                'github_url' => null,
                'live_url' => 'https://project3.example.com',
                'technologies' => json_encode(['Figma', 'Adobe XD', 'Sketch']),
                'order' => 3,
                'is_featured' => false,
                'is_active' => true
            ]
        ];
        
        foreach ($projects as $project) {
            ProjectSection::create($project);
        }
        
        $this->info('Projects created successfully!');
    }
    
    /**
     * Create test skills data
     */
    private function createSkills()
    {
        if (SkillSection::count() > 0) {
            $this->info('Skills already exist!');
            return;
        }
        
        $skills = [
            [
                'name' => '3D Modeling',
                'icon' => 'cube',
                'proficiency' => 90,
                'category' => 'design',
                'order' => 1,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'name' => 'React',
                'icon' => 'code',
                'proficiency' => 85,
                'category' => 'development',
                'order' => 2,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'name' => 'Laravel',
                'icon' => 'server',
                'proficiency' => 80,
                'category' => 'development',
                'order' => 3,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'name' => 'UI/UX Design',
                'icon' => 'palette',
                'proficiency' => 85,
                'category' => 'design',
                'order' => 4,
                'is_featured' => true,
                'is_active' => true
            ]
        ];
        
        foreach ($skills as $skill) {
            SkillSection::create($skill);
        }
        
        $this->info('Skills created successfully!');
    }
    
    /**
     * Create test about data
     */
    private function createAbout()
    {
        if (AboutSection::first()) {
            $this->info('About section already exists!');
            return;
        }
        
        $about = new AboutSection();
        $about->name = 'Aziz Khan';
        $about->title = 'Full Stack Developer & 3D Artist';
        $about->bio = 'I am a passionate developer with experience in building web applications and creating 3D visualizations.';
        $about->profile_image = '/images/profile.jpg';
        $about->education = json_encode([
            [
                'institution' => 'University of Technology',
                'degree' => 'Bachelor of Computer Science',
                'year' => '2018-2022',
                'description' => 'Graduated with honors'
            ]
        ]);
        $about->experience = json_encode([
            [
                'company' => 'Tech Solutions Inc.',
                'position' => 'Senior Developer',
                'year' => '2022-Present',
                'description' => 'Working on enterprise web applications'
            ],
            [
                'company' => 'Creative Studio',
                'position' => '3D Artist',
                'year' => '2020-2022',
                'description' => 'Created 3D visualizations for marketing materials'
            ]
        ]);
        $about->social_links = json_encode([
            'github' => 'https://github.com/username',
            'linkedin' => 'https://linkedin.com/in/username',
            'twitter' => 'https://twitter.com/username'
        ]);
        $about->is_active = true;
        $about->save();
        
        $this->info('About section created successfully!');
    }
    
    /**
     * Create test services data
     */
    private function createServices()
    {
        if (ServiceSection::count() > 0) {
            $this->info('Services already exist!');
            return;
        }
        
        $services = [
            [
                'title' => '3D Modeling & Animation',
                'description' => 'High-quality 3D models and animations for games, films, and product visualization.',
                'icon' => 'cube',
                'order' => 1,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'title' => 'Web Development',
                'description' => 'Custom websites and web applications using modern frameworks and technologies.',
                'icon' => 'code',
                'order' => 2,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'title' => 'UI/UX Design',
                'description' => 'User-centered design solutions that enhance user experience and engagement.',
                'icon' => 'palette',
                'order' => 3,
                'is_featured' => true,
                'is_active' => true
            ],
            [
                'title' => 'Brand Identity',
                'description' => 'Comprehensive brand identity packages including logos, style guides, and marketing materials.',
                'icon' => 'star',
                'order' => 4,
                'is_featured' => false,
                'is_active' => true
            ]
        ];
        
        foreach ($services as $service) {
            ServiceSection::create($service);
        }
        
        $this->info('Services created successfully!');
    }
    
    /**
     * Create test contact data
     */
    private function createContact()
    {
        if (ContactSection::first()) {
            $this->info('Contact section already exists!');
            return;
        }
        
        $contact = new ContactSection();
        $contact->email = '<EMAIL>';
        $contact->phone = '+****************';
        $contact->address = '123 Main St, City, Country';
        $contact->social_links = json_encode([
            'github' => 'https://github.com/username',
            'linkedin' => 'https://linkedin.com/in/username',
            'twitter' => 'https://twitter.com/username'
        ]);
        $contact->show_contact_form = true;
        $contact->form_recipient_email = '<EMAIL>';
        $contact->is_active = true;
        $contact->save();
        
        $this->info('Contact section created successfully!');
    }
}
