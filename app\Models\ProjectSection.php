<?php

namespace App\Models;

use App\Helpers\FileHelper;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class ProjectSection extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'title',
        'description',
        'image_path',
        'github_url',
        'live_url',
        'technologies',
        'order',
        'is_featured',
        'is_active'
    ];
    
    protected $casts = [
        'technologies' => 'json',
        'is_featured' => 'boolean',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get the image path with proper URL formatting
     *
     * @param string|null $value
     * @return string
     */
    public function getImagePathAttribute($value)
    {
        if (empty($value)) {
            return '/images/project.jpg';
        }
        
        try {
            return FileHelper::getPublicUrl($value, '/images/project.jpg');
        } catch (\Exception $e) {
            Log::error('Error processing project image URL: ' . $e->getMessage());
            return '/images/project.jpg';
        }
    }
}
