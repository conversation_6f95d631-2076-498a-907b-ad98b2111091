import { useState, useEffect } from 'react';
import { Edit, ExternalLink, Loader2, Pencil, Plus, Save, Trash, Trash2 } from 'lucide-react';
import { router, usePage } from '@inertiajs/react';
import { useToast } from '@/components/ui/use-toast';
import { FileUpload } from '@/components/ui/file-upload';

import AdminLayout from './layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
    Dialog, 
    DialogContent, 
    DialogDescription, 
    DialogFooter, 
    DialogHeader, 
    DialogTitle, 
    DialogTrigger 
} from '@/components/ui/dialog';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Project type definition
interface Project {
    id: number;
    title: string;
    description: string;
    image_path: string;
    github_url: string;
    live_url: string;
    technologies: string[];
    order: number;
    is_featured: boolean;
    is_active: boolean;
    created_at?: string;
    updated_at?: string;
}

// Default empty project for new project form
const emptyProject: Project = {
    id: 0,
    title: '',
    description: 'Project description',  // Initialize with a default value to avoid validation errors
    image_path: '',
    github_url: '',
    live_url: '',
    technologies: [],
    order: 0,
    is_featured: false,
    is_active: true
};

interface ProjectsProps {
    projects?: Project[];
}

export default function Projects({ projects: backendProjects = [] }: ProjectsProps) {
    const { toast } = useToast();
    const { errors } = usePage().props as any;
    
    // State for projects
    const [projects, setProjects] = useState<Project[]>(() => {
        if (backendProjects && backendProjects.length > 0) {
            return backendProjects.map(project => ({
                ...project,
                technologies: typeof project.technologies === 'string' 
                    ? JSON.parse(project.technologies) 
                    : project.technologies
            }));
        }
        return [];
    });
    
    // State for project form
    const [isOpen, setIsOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [currentProject, setCurrentProject] = useState<Project>(emptyProject);
    const [isSaving, setIsSaving] = useState(false);
    
    // Show error toast if there are validation errors
    useEffect(() => {
        if (errors && Object.keys(errors).length > 0) {
            toast({
                title: "Validation Error",
                description: "Please check the form for errors.",
                variant: "destructive"
            });
        }
    }, [errors, toast]);
    
    // Open new project form
    const openNewProjectForm = () => {
        // Create a fresh copy of the empty project template with a default description
        setCurrentProject({
            ...emptyProject,
            description: 'Project description' // Ensure description is always initialized
        });
        setIsEditing(false);
        setIsOpen(true);
    };
    
    // Open edit project form
    const openEditProjectForm = (project: Project) => {
        setCurrentProject(project);
        setIsEditing(true);
        setIsOpen(true);
    };
    
    // Handle input change
    const handleInputChange = (key: keyof Project, value: any) => {
        setCurrentProject(prev => ({
            ...prev,
            [key]: value
        }));
    };
    
    // Handle technologies change
    const handleTechnologiesChange = (value: string) => {
        const techArray = value.split(',').map(tech => tech.trim()).filter(Boolean);
        setCurrentProject(prev => ({
            ...prev,
            technologies: techArray
        }));
    };
    
    // Validate form - returns validation errors for display in UI
    const validateForm = () => {
        const errors: Record<string, string> = {};
        
        if (!currentProject.title || !currentProject.title.trim()) {
            errors.title = 'Title is required';
        }
        
        if (!currentProject.description || !currentProject.description.trim()) {
            errors.description = 'Description is required';
        }
        
        if (!currentProject.technologies || currentProject.technologies.length === 0) {
            errors.technologies = 'At least one technology is required';
        }
        
        return {
            isValid: Object.keys(errors).length === 0,
            errors
        };
    };
    
    // Check if form has any validation errors
    const hasValidationErrors = () => {
        const { isValid } = validateForm();
        return !isValid;
    };
    
    // Save project
    const saveProject = () => {
        // Client-side validation
        if (!currentProject.title || !currentProject.title.trim()) {
            toast({
                title: "Validation Error",
                description: "Title is required",
                variant: "destructive"
            });
            return;
        }
        
        if (!currentProject.description || !currentProject.description.trim()) {
            toast({
                title: "Validation Error",
                description: "Description is required",
                variant: "destructive"
            });
            return;
        }
        
        setIsSaving(true);
        
        // Prepare data for backend - using FormData to ensure proper handling
        const formData = new FormData();
        formData.append('title', currentProject.title.trim());
        formData.append('description', currentProject.description.trim());
        formData.append('image_path', currentProject.image_path || '');
        formData.append('github_url', currentProject.github_url || '');
        formData.append('live_url', currentProject.live_url || '');
        formData.append('technologies', JSON.stringify(currentProject.technologies || []));
        formData.append('order', String(currentProject.order || 0));
        formData.append('is_featured', currentProject.is_featured ? '1' : '0');
        formData.append('is_active', currentProject.is_active ? '1' : '0');
        
        console.log('Submitting project data');
        
        // Convert FormData to object for debugging
        const formDataObj: Record<string, any> = {};
        formData.forEach((value, key) => {
            formDataObj[key] = value;
        });
        console.log('Form data:', formDataObj);
        
        if (isEditing) {
            // Update existing project
            router.post(`/admin/projects/${currentProject.id}?_method=PUT`, formData, {
                onSuccess: () => {
                    setIsSaving(false);
                    setIsOpen(false);
                    toast({
                        title: "Project Updated",
                        description: "Project has been updated successfully."
                    });
                },
                onError: (errors: any) => {
                    setIsSaving(false);
                    console.error('Save errors:', errors);
                    
                    // Format error messages for display
                    let errorMessage = "There was a problem saving your changes.";
                    if (errors && typeof errors === 'object') {
                        const errorMessages = [];
                        for (const key in errors) {
                            if (Array.isArray(errors[key])) {
                                errorMessages.push(...errors[key]);
                            } else if (typeof errors[key] === 'string') {
                                errorMessages.push(errors[key]);
                            }
                        }
                        if (errorMessages.length > 0) {
                            errorMessage = errorMessages.join('\n');
                        }
                    }
                    
                    toast({
                        title: "Error saving changes",
                        description: errorMessage,
                        variant: "destructive"
                    });
                }
            });
        } else {
            // Create new project
            router.post('/admin/projects', formData, {
                onSuccess: () => {
                    setIsSaving(false);
                    setIsOpen(false);
                    toast({
                        title: "Project Created",
                        description: "New project has been created successfully."
                    });
                },
                onError: (errors: any) => {
                    setIsSaving(false);
                    console.error('Save errors:', errors);
                    
                    // Format error messages for display
                    let errorMessage = "There was a problem saving your changes.";
                    if (errors && typeof errors === 'object') {
                        const errorMessages = [];
                        for (const key in errors) {
                            if (Array.isArray(errors[key])) {
                                errorMessages.push(...errors[key]);
                            } else if (typeof errors[key] === 'string') {
                                errorMessages.push(errors[key]);
                            }
                        }
                        if (errorMessages.length > 0) {
                            errorMessage = errorMessages.join('\n');
                        }
                    }
                    
                    toast({
                        title: "Error saving changes",
                        description: errorMessage,
                        variant: "destructive"
                    });
                }
            });
        }
    };
    
    // Delete project
    const deleteProject = (id: number) => {
        if (confirm('Are you sure you want to delete this project?')) {
            router.delete(`/admin/projects/${id}`, {
                onSuccess: () => {
                    toast({
                        title: "Project Deleted",
                        description: "Project has been deleted successfully."
                    });
                },
                onError: (errors: Record<string, string>) => {
                    console.error('Delete errors:', errors);
                    toast({
                        title: "Error deleting project",
                        description: "There was a problem deleting the project. Please try again.",
                        variant: "destructive"
                    });
                }
            });
        }
    };
    
    return (
        <AdminLayout title="Projects" currentPage="projects">
            <div className="mb-6 flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Projects</h1>
                    <p className="text-gray-500 dark:text-gray-400">Manage your portfolio projects</p>
                </div>
                <Button onClick={openNewProjectForm} className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add Project
                </Button>
            </div>
            
            {/* Project list */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {projects.map(project => (
                    <Card key={project.id} className="overflow-hidden">
                        {project.image_path && (
                            <div className="aspect-video w-full overflow-hidden">
                                <img 
                                    src={project.image_path} 
                                    alt={project.title} 
                                    className="h-full w-full object-cover transition-all hover:scale-105"
                                />
                            </div>
                        )}
                        <CardContent className="p-6">
                            <div className="flex items-start justify-between">
                                <div>
                                    <h3 className="font-semibold text-lg mb-2">{project.title}</h3>
                                    <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2 mb-3">
                                        {project.description}
                                    </p>
                                </div>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="icon">
                                            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path></svg>
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => openEditProjectForm(project)}>
                                            <Pencil className="mr-2 h-4 w-4" />
                                            Edit
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => deleteProject(project.id)}>
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Delete
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                            
                            <div className="flex flex-wrap gap-2 mt-4">
                                {project.technologies.map((tech, index) => (
                                    <Badge key={index} variant="secondary">{tech}</Badge>
                                ))}
                            </div>
                            
                            <div className="flex gap-2 mt-4">
                                {project.github_url && (
                                    <Button variant="outline" size="sm" asChild>
                                        <a href={project.github_url} target="_blank" rel="noopener noreferrer">
                                            GitHub
                                        </a>
                                    </Button>
                                )}
                                {project.live_url && (
                                    <Button variant="outline" size="sm" asChild>
                                        <a href={project.live_url} target="_blank" rel="noopener noreferrer">
                                            Live Demo
                                            <ExternalLink className="ml-1 h-3 w-3" />
                                        </a>
                                    </Button>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                ))}
            </div>
            
            {/* Project form dialog */}
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>{isEditing ? 'Edit Project' : 'Add New Project'}</DialogTitle>
                        <DialogDescription>
                            {isEditing ? 'Update the details of your project.' : 'Add a new project to your portfolio.'}
                        </DialogDescription>
                    </DialogHeader>
                    
                    <div className="grid gap-4 py-4">
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="title" className="text-right">
                                Title
                            </Label>
                            <Input
                                id="title"
                                value={currentProject.title}
                                onChange={(e) => handleInputChange('title', e.target.value)}
                                className="col-span-3"
                            />
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="description" className="text-right">
                                Description <span className="text-red-500">*</span>
                            </Label>
                            <div className="col-span-3 space-y-1">
                                <Textarea
                                    id="description"
                                    value={currentProject.description || ''}
                                    onChange={(e) => handleInputChange('description', e.target.value)}
                                    className={`w-full ${!currentProject.description ? 'border-red-500 focus:ring-red-500' : ''}`}
                                    placeholder="Enter project description (required)"
                                    rows={4}
                                    required
                                />
                                {!currentProject.description && (
                                    <p className="text-sm text-red-500">Description is required</p>
                                )}
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="image_path" className="text-right">
                                Project Image
                            </Label>
                            <div className="col-span-3">
                                <FileUpload
                                    onFileUploaded={(path) => handleInputChange('image_path', path)}
                                    currentValue={currentProject.image_path}
                                    accept="image/*"
                                    label="Upload Project Image"
                                    uploadPath="/admin/upload"
                                />
                                {currentProject.image_path && (
                                    <div className="mt-2 text-sm text-gray-500">
                                        Current image: {currentProject.image_path}
                                    </div>
                                )}
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="github" className="text-right">
                                GitHub URL
                            </Label>
                            <Input
                                id="github"
                                value={currentProject.github_url}
                                onChange={(e) => handleInputChange('github_url', e.target.value)}
                                className="col-span-3"
                                placeholder="https://github.com/username/repo"
                            />
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="live" className="text-right">
                                Live URL
                            </Label>
                            <Input
                                id="live"
                                value={currentProject.live_url}
                                onChange={(e) => handleInputChange('live_url', e.target.value)}
                                className="col-span-3"
                                placeholder="https://example.com"
                            />
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="technologies" className="text-right">
                                Technologies
                            </Label>
                            <Input
                                id="technologies"
                                value={currentProject.technologies.join(', ')}
                                onChange={(e) => handleTechnologiesChange(e.target.value)}
                                className="col-span-3"
                                placeholder="React, Node.js, MongoDB"
                            />
                            <div className="col-span-3 col-start-2 text-xs text-gray-500">
                                Separate technologies with commas
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="order" className="text-right">
                                Order
                            </Label>
                            <Input
                                id="order"
                                type="number"
                                value={currentProject.order.toString()}
                                onChange={(e) => handleInputChange('order', parseInt(e.target.value) || 0)}
                                className="col-span-3"
                            />
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="featured" className="text-right">
                                Featured
                            </Label>
                            <div className="flex items-center space-x-2 col-span-3">
                                <input
                                    type="checkbox"
                                    id="featured"
                                    checked={currentProject.is_featured}
                                    onChange={(e) => handleInputChange('is_featured', e.target.checked)}
                                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                />
                                <Label htmlFor="featured" className="text-sm font-normal">
                                    Show this project in featured section
                                </Label>
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-4 items-center gap-4">
                            <Label htmlFor="active" className="text-right">
                                Active
                            </Label>
                            <div className="flex items-center space-x-2 col-span-3">
                                <input
                                    type="checkbox"
                                    id="active"
                                    checked={currentProject.is_active}
                                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                                    className="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                />
                                <Label htmlFor="active" className="text-sm font-normal">
                                    Show this project on the website
                                </Label>
                            </div>
                        </div>
                    </div>
                    
                    <DialogFooter>
                        <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                            Cancel
                        </Button>
                        <Button 
                            type="button" 
                            onClick={saveProject} 
                            disabled={isSaving || hasValidationErrors()}
                            className={hasValidationErrors() ? 'opacity-50 cursor-not-allowed' : ''}
                        >
                            {isSaving ? (
                                <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="mr-2 h-4 w-4" />
                                    {isEditing ? 'Update Project' : 'Save Project'}
                                </>
                            )}
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </AdminLayout>
    );
}
