<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\FileHelper;
use App\Http\Controllers\Controller;
use App\Models\AboutSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class AboutController extends Controller
{
    /**
     * Display the about section data
     */
    public function index()
    {
        $aboutSection = AboutSection::first();
        
        if (!$aboutSection) {
            // Create a default about section if none exists
            $aboutSection = new AboutSection([
                'name' => '<PERSON> Doe',
                'title' => 'Full Stack Developer',
                'bio' => 'I am a passionate developer with experience in building web applications.',
                'profile_image' => '/images/profile.jpg',
                'education' => json_encode([
                    [
                        'institution' => 'University of Technology',
                        'degree' => 'Bachelor of Computer Science',
                        'year' => '2018-2022',
                        'description' => 'Graduated with honors'
                    ]
                ]),
                'experience' => json_encode([
                    [
                        'company' => 'Tech Solutions Inc.',
                        'position' => 'Senior Developer',
                        'year' => '2022-Present',
                        'description' => 'Working on enterprise web applications'
                    ]
                ]),
                'social_links' => json_encode([
                    'github' => 'https://github.com/johndoe',
                    'linkedin' => 'https://linkedin.com/in/johndoe',
                    'twitter' => 'https://twitter.com/johndoe'
                ]),
                'is_active' => true
            ]);
            $aboutSection->save();
        }
        
        // Process the profile image URL to ensure it's properly formatted
        $aboutSection->profile_image = FileHelper::getPublicUrl($aboutSection->profile_image, '/images/profile.jpg');
        
        // Debug information
        \Log::info('About Section Data:', ['data' => $aboutSection->toArray()]);
        
        return Inertia::render('admin/about', [
            'title' => 'About Me',
            'aboutData' => $aboutSection
        ]);
    }
    

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request)
    {
        // Debug request data
        \Log::info('About Update Request:', ['data' => $request->all()]);
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'bio' => 'required|string',
            'profile_image' => 'nullable|string',
            'education' => 'required|string',  // Changed from array to string for JSON
            'experience' => 'required|string',  // Changed from array to string for JSON
            'social_links' => 'required|string',  // Changed from array to string for JSON
            'is_active' => 'boolean'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed:', ['errors' => $validator->errors()->toArray()]);
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        $aboutSection = AboutSection::first();
        
        if (!$aboutSection) {
            $aboutSection = new AboutSection();
        }
        
        $aboutSection->name = $request->name;
        $aboutSection->title = $request->title;
        $aboutSection->bio = $request->bio;
        $aboutSection->profile_image = $request->profile_image;
        $aboutSection->education = $request->education; // Already a JSON string from frontend
        $aboutSection->experience = $request->experience; // Already a JSON string from frontend
        $aboutSection->social_links = $request->social_links; // Already a JSON string from frontend
        $aboutSection->is_active = $request->is_active ?? true;
        
        try {
            $aboutSection->save();
            \Log::info('About section saved successfully', ['id' => $aboutSection->id]);
            return redirect()->back()->with('success', 'About section updated successfully');
        } catch (\Exception $e) {
            \Log::error('Error saving about section:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to save about section: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
