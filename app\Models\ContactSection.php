<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContactSection extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'title',
        'subtitle',
        'email',
        'phone',
        'address',
        'social_links',
        'show_contact_form',
        'form_recipient_email',
        'is_active'
    ];
    
    protected $casts = [
        'social_links' => 'json',
        'show_contact_form' => 'boolean',
        'is_active' => 'boolean'
    ];
}
