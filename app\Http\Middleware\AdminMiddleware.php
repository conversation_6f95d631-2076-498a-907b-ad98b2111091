<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated and is an admin
        // Primary check: is_admin field
        // Fallback check: specific admin email addresses for backward compatibility
        $adminEmails = ['<EMAIL>']; // Add all admin email addresses here
        
        if (!$request->user() || 
            (!$request->user()->is_admin && !in_array($request->user()->email, $adminEmails))) {
            return redirect()->route('admin.login');
        }

        return $next($request);
    }
}
