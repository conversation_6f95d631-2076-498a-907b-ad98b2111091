<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    try {
        // Try to render the welcome page with minimal data to avoid database issues
        return Inertia::render('welcome', [
            'heroSection' => [
                'title' => 'Welcome to Our Site',
                'subtitle' => 'Portfolio & Web Development',
                'model_settings' => '{}',
                'background_settings' => '{}',
                'is_active' => true
            ],
            'projectSections' => [],
            'skillSections' => [],
            'aboutSection' => [
                'title' => 'About Us',
                'subtitle' => 'Professional Services',
                'content' => 'Welcome to our portfolio website.',
                'profile_image' => null,
                'resume_link' => null
            ],
            'serviceSections' => [],
            'contactSection' => [
                'title' => 'Contact Us',
                'subtitle' => 'Get in touch',
                'email' => '<EMAIL>',
                'phone' => '+1234567890',
                'address' => 'Your Address'
            ],
            'settings' => [
                'site_name' => 'Portfolio Site',
                'site_description' => 'Professional Portfolio',
                'site_keywords' => 'portfolio, web development',
                'site_logo' => null,
                'site_favicon' => null,
                'primary_color' => '#3b82f6',
                'secondary_color' => '#8b5cf6',
                'google_analytics_id' => '',
                'meta_image' => null,
                'custom_css' => '',
                'custom_js' => '',
                'maintenance_mode' => false
            ],
            'skillSettings' => [
                'skills_visualization_type' => 'bars',
                'skills_primary_color' => '#3b82f6',
                'skills_secondary_color' => '#8b5cf6',
                'skills_show_categories' => true,
                'skills_show_radar' => false,
                'skills_show_bars' => true,
                'skills_max_display' => 12,
                'skills_custom_css' => '',
                'skills_animate_points' => true
            ],
            'footerSettings' => [
                'logo_path' => null,
                'logo_size' => 40,
                'description' => 'Professional portfolio website',
                'copyright_text' => '© 2024 Portfolio Site. All rights reserved.',
                'social_links' => [],
                'quick_links' => [],
                'contact_info' => [
                    'email' => '<EMAIL>',
                    'phone' => '+1234567890',
                    'address' => 'Your Address'
                ],
                'is_active' => true
            ],
            'captcha' => [
                'question' => '3 + 4',
                'answer' => 7
            ]
        ]);
    } catch (\Exception $e) {
        // If there's any error, return a simple response
        return response()->json([
            'error' => 'Application error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('home');

// Admin routes
Route::get('/admin/login', function () {
    try {
        return Inertia::render('admin/login');
    } catch (\Exception $e) {
        return response()->json([
            'error' => 'Admin login error: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ], 500);
    }
})->name('admin.login');
Route::post('/admin/login', [\App\Http\Controllers\Admin\AuthController::class, 'login'])
    ->name('admin.login.submit');
Route::post('/admin/logout', [\App\Http\Controllers\Admin\AuthController::class, 'logout'])
    ->name('admin.logout');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Admin dashboard - protected by AdminMiddleware
    Route::middleware([\App\Http\Middleware\AdminMiddleware::class])->group(function () {
        Route::get('admin/dashboard', function () {
            // Get optimized data for the admin dashboard
            $recentProjects = \App\Models\ProjectSection::select(['id', 'title', 'created_at', 'updated_at'])
                ->orderBy('created_at', 'desc')
                ->limit(5) // Limit to improve performance
                ->get();

            $recentMessages = \App\Models\ContactMessage::select(['id', 'name', 'email', 'subject', 'created_at'])
                ->orderBy('created_at', 'desc')
                ->limit(5) // Limit to improve performance
                ->get();

            $stats = [
                'projects' => \App\Models\ProjectSection::count(),
                'skills' => \App\Models\SkillSection::count(),
                'messages' => \App\Models\ContactMessage::count(),
                'visitors' => \App\Models\Visitor::count() ?? 0
            ];

            // Explicitly render the admin dashboard component with optimized data
            return Inertia::render('admin/dashboard', [
                'title' => 'Admin Dashboard',
                'isAdmin' => true, // Flag to identify admin dashboard
                'recentProjects' => $recentProjects,
                'recentMessages' => $recentMessages,
                'stats' => $stats
            ]);
        })->name('admin.dashboard');

        // Front page section management routes
        Route::get('admin/hero', [\App\Http\Controllers\Admin\HeroController::class, 'index'])->name('admin.hero');
        Route::post('admin/hero', [\App\Http\Controllers\Admin\HeroController::class, 'update'])->name('admin.hero.update');

        Route::get('admin/projects', [\App\Http\Controllers\Admin\ProjectController::class, 'index'])->name('admin.projects');
        Route::post('admin/projects', [\App\Http\Controllers\Admin\ProjectController::class, 'store'])->name('admin.projects.store');
        Route::put('admin/projects/{id}', [\App\Http\Controllers\Admin\ProjectController::class, 'update'])->name('admin.projects.update');
        Route::delete('admin/projects/{id}', [\App\Http\Controllers\Admin\ProjectController::class, 'destroy'])->name('admin.projects.destroy');

        Route::get('admin/skills', [\App\Http\Controllers\Admin\SkillController::class, 'index'])->name('admin.skills');
        Route::post('admin/skills', [\App\Http\Controllers\Admin\SkillController::class, 'store'])->name('admin.skills.store');
        Route::put('admin/skills/{id}', [\App\Http\Controllers\Admin\SkillController::class, 'update'])->name('admin.skills.update');
        Route::delete('admin/skills/{id}', [\App\Http\Controllers\Admin\SkillController::class, 'destroy'])->name('admin.skills.destroy');

        Route::get('admin/skill-settings', [\App\Http\Controllers\Admin\SkillSettingsController::class, 'index'])->name('admin.skill-settings');
        Route::post('admin/skill-settings', [\App\Http\Controllers\Admin\SkillSettingsController::class, 'update'])->name('admin.skill-settings.update');

        Route::get('admin/about', [\App\Http\Controllers\Admin\AboutController::class, 'index'])->name('admin.about');
        Route::post('admin/about', [\App\Http\Controllers\Admin\AboutController::class, 'update'])->name('admin.about.update');

        Route::get('admin/services', [\App\Http\Controllers\Admin\ServiceController::class, 'index'])->name('admin.services');
        Route::post('admin/services', [\App\Http\Controllers\Admin\ServiceController::class, 'store'])->name('admin.services.store');
        Route::put('admin/services/{id}', [\App\Http\Controllers\Admin\ServiceController::class, 'update'])->name('admin.services.update');
        Route::delete('admin/services/{id}', [\App\Http\Controllers\Admin\ServiceController::class, 'destroy'])->name('admin.services.destroy');

        Route::get('admin/contact', [\App\Http\Controllers\Admin\ContactController::class, 'index'])->name('admin.contact');
        Route::post('admin/contact', [\App\Http\Controllers\Admin\ContactController::class, 'update'])->name('admin.contact.update');

        // Messages routes
        Route::get('admin/messages', [\App\Http\Controllers\Admin\MessageController::class, 'index'])->name('admin.messages');
        Route::get('admin/messages/{id}', [\App\Http\Controllers\Admin\MessageController::class, 'show'])->name('admin.messages.show');
        Route::put('admin/messages/{id}', [\App\Http\Controllers\Admin\MessageController::class, 'update'])->name('admin.messages.update');
        Route::delete('admin/messages/{id}', [\App\Http\Controllers\Admin\MessageController::class, 'destroy'])->name('admin.messages.destroy');

        // Settings routes
        Route::get('admin/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'index'])->name('admin.settings');
        Route::post('admin/settings', [\App\Http\Controllers\Admin\SettingsController::class, 'update'])->name('admin.settings.update');

        // Footer settings routes
        Route::get('admin/footer-settings', [\App\Http\Controllers\Admin\FooterSettingController::class, 'index'])->name('admin.footer-settings');
        Route::post('admin/footer-settings', [\App\Http\Controllers\Admin\FooterSettingController::class, 'update'])->name('admin.footer-settings.update');

        // File upload route
        Route::post('admin/upload', [\App\Http\Controllers\Admin\FileUploadController::class, 'upload'])->name('admin.upload');
    });
});

// Contact form routes
Route::get('/contact/captcha', [\App\Http\Controllers\ContactController::class, 'generateCaptcha'])->name('contact.captcha');
Route::post('/contact', [\App\Http\Controllers\ContactController::class, 'submit'])->name('contact.submit');

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
