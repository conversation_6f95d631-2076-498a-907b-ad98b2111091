<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('skill_settings', function (Blueprint $table) {
            // Add missing skills_animate_points column
            if (!Schema::hasColumn('skill_settings', 'skills_animate_points')) {
                $table->boolean('skills_animate_points')->default(true);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('skill_settings', function (Blueprint $table) {
            $table->dropColumn('skills_animate_points');
        });
    }
};
