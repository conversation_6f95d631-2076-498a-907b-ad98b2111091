<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\SkillSection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class SkillController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $skills = SkillSection::orderBy('order')->get();
        
        // Debug information
        \Log::info('Skills Data:', ['count' => $skills->count()]);
        
        return Inertia::render('admin/skills', [
            'title' => 'Skills',
            'skills' => $skills
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Debug request data
        \Log::info('Skill Store Request:', ['data' => $request->all()]);
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string',
            'proficiency' => 'required|integer|min:1|max:100',
            'category' => 'nullable|string',
            'order' => 'nullable|integer',
            'is_active' => 'boolean',
            'is_featured' => 'boolean'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed:', ['errors' => $validator->errors()->toArray()]);
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        try {
            // Get the highest order value
            $maxOrder = SkillSection::max('order') ?? 0;
            
            $skill = new SkillSection();
            $skill->name = $request->name;
            $skill->icon = $request->icon;
            $skill->proficiency = $request->proficiency;
            $skill->category = $request->category ?? 'development';
            $skill->order = $request->order ?? ($maxOrder + 1);
            $skill->is_active = $request->has('is_active') ? $request->is_active : true;
            $skill->is_featured = $request->has('is_featured') ? $request->is_featured : false;
            $skill->save();
            
            \Log::info('Skill created successfully', ['id' => $skill->id]);
            return redirect()->back()->with('success', 'Skill created successfully');
        } catch (\Exception $e) {
            \Log::error('Error creating skill:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to create skill: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $skill = SkillSection::findOrFail($id);
        return response()->json($skill);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Debug request data
        \Log::info('Skill Update Request:', ['id' => $id, 'data' => $request->all()]);
        
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'icon' => 'nullable|string',
            'proficiency' => 'required|integer|min:1|max:100',
            'category' => 'nullable|string',
            'order' => 'nullable|integer',
            'is_active' => 'boolean',
            'is_featured' => 'boolean'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed:', ['errors' => $validator->errors()->toArray()]);
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        try {
            $skill = SkillSection::findOrFail($id);
            $skill->name = $request->name;
            $skill->icon = $request->icon;
            $skill->proficiency = $request->proficiency;
            $skill->category = $request->category ?? $skill->category;
            $skill->order = $request->order ?? $skill->order;
            $skill->is_active = $request->has('is_active') ? $request->is_active : $skill->is_active;
            $skill->is_featured = $request->has('is_featured') ? $request->is_featured : $skill->is_featured;
            $skill->save();
            
            \Log::info('Skill updated successfully', ['id' => $skill->id]);
            return redirect()->back()->with('success', 'Skill updated successfully');
        } catch (\Exception $e) {
            \Log::error('Error updating skill:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to update skill: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $skill = SkillSection::findOrFail($id);
            $skill->delete();
            
            \Log::info('Skill deleted successfully', ['id' => $id]);
            return redirect()->back()->with('success', 'Skill deleted successfully');
        } catch (\Exception $e) {
            \Log::error('Error deleting skill:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to delete skill: ' . $e->getMessage()]);
        }
    }
}
