<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;
use Inertia\Response;

class AuthController extends Controller
{
    /**
     * Show the admin login page.
     */
    public function showLoginForm(): Response
    {
        return Inertia::render('admin/login', [
            'canResetPassword' => Route::has('password.request'),
        ]);
    }

    /**
     * Handle an incoming admin authentication request.
     */
    public function login(LoginRequest $request): RedirectResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        // Check if the user is an admin
        // Primary check: is_admin field
        // Fallback check: specific admin email addresses for backward compatibility
        $adminEmails = ['<EMAIL>']; // Add all admin email addresses here
        $user = Auth::user();
        
        if (!$user->is_admin && !in_array($user->email, $adminEmails)) {
            // If not an admin, update the user record if they're in the admin emails list
            if (in_array($user->email, $adminEmails)) {
                $user->is_admin = true;
                $user->save();
            } else {
                Auth::logout();
                $request->session()->invalidate();
                $request->session()->regenerateToken();
                
                return redirect()->route('admin.login')
                    ->withErrors(['email' => 'You do not have admin privileges.']);
            }
        }

        // Explicitly redirect to the admin dashboard URL, not just the route name
        return redirect()->intended('/admin/dashboard');
    }

    /**
     * Destroy an authenticated session.
     */
    public function logout(Request $request): RedirectResponse
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect()->route('admin.login');
    }
}
