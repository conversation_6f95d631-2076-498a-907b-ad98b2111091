import { useState, useEffect } from 'react';
import { router, usePage } from '@inertiajs/react';
import { FormDataConvertible } from '@inertiajs/core';
import { Save, Upload } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { FileUpload } from '@/components/ui/file-upload';

import AdminLayout from './layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Settings type definition
interface Settings {
    id?: number;
    site_name: string;
    site_description: string;
    site_keywords: string;
    site_logo: string;
    site_favicon: string;
    primary_color: string;
    secondary_color: string;
    google_analytics_id: string;
    meta_image: string;
    custom_css: string;
    custom_js: string;
    maintenance_mode: boolean;
    created_at?: string;
    updated_at?: string;
}

interface SettingsProps {
    settings?: Settings;
}

export default function Settings({ settings: backendSettings }: SettingsProps) {
    const { toast } = useToast();
    const { errors } = usePage().props as any;
    
    // Default settings
    const defaultSettings: Settings = {
        site_name: 'My Portfolio',
        site_description: 'My personal portfolio website',
        site_keywords: 'portfolio, web developer, designer',
        site_logo: '/img/logo.png',
        site_favicon: '/img/favicon.ico',
        primary_color: '#3b82f6',
        secondary_color: '#10b981',
        google_analytics_id: '',
        meta_image: '/img/meta-image.jpg',
        custom_css: '',
        custom_js: '',
        maintenance_mode: false
    };
    
    // State for settings
    const [settings, setSettings] = useState<Settings>(backendSettings || defaultSettings);
    const [isSaving, setIsSaving] = useState(false);
    
    // Show error toast if there are validation errors
    useEffect(() => {
        if (errors && Object.keys(errors).length > 0) {
            toast({
                title: "Validation Error",
                description: "Please check the form for errors.",
                variant: "destructive"
            });
        }
    }, [errors, toast]);
    
    // Handle input change
    const handleInputChange = (key: keyof Settings, value: any) => {
        setSettings(prev => ({
            ...prev,
            [key]: value
        }));
    };
    
    // Save settings
    const saveSettings = () => {
        setIsSaving(true);
        
        // Convert settings to a plain object to satisfy the RequestPayload type
        const settingsData = { ...settings } as Record<string, FormDataConvertible>;
        
        router.post('/admin/settings', settingsData, {
            onSuccess: () => {
                setIsSaving(false);
                toast({
                    title: "Settings Saved",
                    description: "Your settings have been saved successfully."
                });
            },
            onError: () => {
                setIsSaving(false);
                toast({
                    title: "Error Saving Settings",
                    description: "There was a problem saving your settings. Please try again.",
                    variant: "destructive"
                });
            }
        });
    };
    
    return (
        <AdminLayout title="Settings" currentPage="settings">
            <div className="mb-6 flex items-center justify-between">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
                    <p className="text-gray-500 dark:text-gray-400">Manage your site settings</p>
                </div>
                <Button onClick={saveSettings} disabled={isSaving} className="flex items-center gap-2">
                    {isSaving ? (
                        <>
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Saving...
                        </>
                    ) : (
                        <>
                            <Save className="h-4 w-4" />
                            Save Settings
                        </>
                    )}
                </Button>
            </div>
            
            <Tabs defaultValue="general">
                <TabsList className="mb-4">
                    <TabsTrigger value="general">General</TabsTrigger>
                    <TabsTrigger value="appearance">Appearance</TabsTrigger>
                    <TabsTrigger value="seo">SEO & Analytics</TabsTrigger>
                    <TabsTrigger value="advanced">Advanced</TabsTrigger>
                </TabsList>
                
                {/* General Settings */}
                <TabsContent value="general">
                    <Card>
                        <CardHeader>
                            <CardTitle>General Settings</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="site_name">Site Name</Label>
                                    <Input
                                        id="site_name"
                                        value={settings.site_name}
                                        onChange={(e) => handleInputChange('site_name', e.target.value)}
                                    />
                                    {errors.site_name && (
                                        <p className="text-sm text-red-500">{errors.site_name}</p>
                                    )}
                                </div>
                                
                                <div className="space-y-2">
                                    <Label htmlFor="maintenance_mode">Maintenance Mode</Label>
                                    <div className="flex items-center space-x-2">
                                        <Switch
                                            id="maintenance_mode"
                                            checked={settings.maintenance_mode}
                                            onCheckedChange={(checked) => handleInputChange('maintenance_mode', checked)}
                                        />
                                        <Label htmlFor="maintenance_mode" className="text-sm font-normal">
                                            Enable maintenance mode
                                        </Label>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="site_description">Site Description</Label>
                                <Textarea
                                    id="site_description"
                                    value={settings.site_description}
                                    onChange={(e) => handleInputChange('site_description', e.target.value)}
                                    rows={3}
                                />
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
                
                {/* Appearance Settings */}
                <TabsContent value="appearance">
                    <Card>
                        <CardHeader>
                            <CardTitle>Appearance Settings</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="site_logo">Site Logo</Label>
                                    <FileUpload
                                        onFileUploaded={(path) => handleInputChange('site_logo', path)}
                                        currentValue={settings.site_logo}
                                        accept="image/*"
                                        label="Upload Logo"
                                        uploadPath="/admin/upload"
                                    />
                                    {settings.site_logo && (
                                        <div className="mt-2 text-sm text-gray-500">
                                            Current logo: {settings.site_logo}
                                        </div>
                                    )}
                                </div>
                                
                                <div className="space-y-2">
                                    <Label htmlFor="site_favicon">Favicon</Label>
                                    <FileUpload
                                        onFileUploaded={(path) => handleInputChange('site_favicon', path)}
                                        currentValue={settings.site_favicon}
                                        accept="image/x-icon,image/png,image/svg+xml"
                                        label="Upload Favicon"
                                        uploadPath="/admin/upload"
                                    />
                                    {settings.site_favicon && (
                                        <div className="mt-2 text-sm text-gray-500">
                                            Current favicon: {settings.site_favicon}
                                        </div>
                                    )}
                                </div>
                            </div>
                            
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="primary_color">Primary Color</Label>
                                    <div className="flex items-center space-x-2">
                                        <Input
                                            id="primary_color"
                                            type="color"
                                            value={settings.primary_color}
                                            onChange={(e) => handleInputChange('primary_color', e.target.value)}
                                            className="w-12 h-10 p-1"
                                        />
                                        <Input
                                            value={settings.primary_color}
                                            onChange={(e) => handleInputChange('primary_color', e.target.value)}
                                            className="flex-1"
                                        />
                                    </div>
                                </div>
                                
                                <div className="space-y-2">
                                    <Label htmlFor="secondary_color">Secondary Color</Label>
                                    <div className="flex items-center space-x-2">
                                        <Input
                                            id="secondary_color"
                                            type="color"
                                            value={settings.secondary_color}
                                            onChange={(e) => handleInputChange('secondary_color', e.target.value)}
                                            className="w-12 h-10 p-1"
                                        />
                                        <Input
                                            value={settings.secondary_color}
                                            onChange={(e) => handleInputChange('secondary_color', e.target.value)}
                                            className="flex-1"
                                        />
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
                
                {/* SEO & Analytics Settings */}
                <TabsContent value="seo">
                    <Card>
                        <CardHeader>
                            <CardTitle>SEO & Analytics Settings</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="site_keywords">Meta Keywords</Label>
                                <Input
                                    id="site_keywords"
                                    value={settings.site_keywords}
                                    onChange={(e) => handleInputChange('site_keywords', e.target.value)}
                                    placeholder="portfolio, web developer, designer"
                                />
                                <p className="text-xs text-gray-500">Separate keywords with commas</p>
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="meta_image">Meta Image URL</Label>
                                <Input
                                    id="meta_image"
                                    value={settings.meta_image}
                                    onChange={(e) => handleInputChange('meta_image', e.target.value)}
                                    placeholder="/img/meta-image.jpg"
                                />
                                <p className="text-xs text-gray-500">Image shown when sharing your site on social media</p>
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="google_analytics_id">Google Analytics ID</Label>
                                <Input
                                    id="google_analytics_id"
                                    value={settings.google_analytics_id}
                                    onChange={(e) => handleInputChange('google_analytics_id', e.target.value)}
                                    placeholder="G-XXXXXXXXXX"
                                />
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
                
                {/* Advanced Settings */}
                <TabsContent value="advanced">
                    <Card>
                        <CardHeader>
                            <CardTitle>Advanced Settings</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="custom_css">Custom CSS</Label>
                                <Textarea
                                    id="custom_css"
                                    value={settings.custom_css}
                                    onChange={(e) => handleInputChange('custom_css', e.target.value)}
                                    rows={6}
                                    className="font-mono text-sm"
                                    placeholder="/* Add your custom CSS here */"
                                />
                            </div>
                            
                            <div className="space-y-2">
                                <Label htmlFor="custom_js">Custom JavaScript</Label>
                                <Textarea
                                    id="custom_js"
                                    value={settings.custom_js}
                                    onChange={(e) => handleInputChange('custom_js', e.target.value)}
                                    rows={6}
                                    className="font-mono text-sm"
                                    placeholder="// Add your custom JavaScript here"
                                />
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </AdminLayout>
    );
}
