<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class MessageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $messages = Message::orderBy('created_at', 'desc')->get();
        
        // Debug information
        \Log::info('Messages Data:', ['count' => $messages->count()]);
        
        return Inertia::render('admin/messages', [
            'title' => 'Messages',
            'messages' => $messages
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $message = Message::findOrFail($id);
        
        // Mark as read if not already
        if (!$message->is_read) {
            $message->is_read = true;
            $message->save();
        }
        
        return response()->json($message);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        // Debug request data
        \Log::info('Message Update Request:', ['id' => $id, 'data' => $request->all()]);
        
        $validator = Validator::make($request->all(), [
            'is_read' => 'boolean',
            'status' => 'string|in:new,read,replied,archived'
        ]);
        
        if ($validator->fails()) {
            \Log::error('Validation failed:', ['errors' => $validator->errors()->toArray()]);
            return redirect()->back()->withErrors($validator)->withInput();
        }
        
        try {
            $message = Message::findOrFail($id);
            
            if ($request->has('is_read')) {
                $message->is_read = $request->is_read;
            }
            
            if ($request->has('status')) {
                $message->status = $request->status;
            }
            
            $message->save();
            
            \Log::info('Message updated successfully', ['id' => $message->id]);
            return redirect()->back()->with('success', 'Message updated successfully');
        } catch (\Exception $e) {
            \Log::error('Error updating message:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to update message: ' . $e->getMessage()])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        try {
            $message = Message::findOrFail($id);
            $message->delete();
            
            \Log::info('Message deleted successfully', ['id' => $id]);
            return redirect()->back()->with('success', 'Message deleted successfully');
        } catch (\Exception $e) {
            \Log::error('Error deleting message:', ['error' => $e->getMessage()]);
            return redirect()->back()->withErrors(['error' => 'Failed to delete message: ' . $e->getMessage()]);
        }
    }
}
