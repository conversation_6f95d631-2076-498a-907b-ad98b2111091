<?php

namespace Database\Seeders;

use App\Models\FooterSetting;
use Illuminate\Database\Seeder;

class FooterSettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing footer settings
        FooterSetting::truncate();
        
        // Create sample footer settings
        FooterSetting::create([
            'logo_path' => null, // Will be uploaded by the user
            'logo_size' => 100,
            'description' => 'Pixel and Code is a web development company specializing in creating beautiful, responsive websites and applications using the latest technologies.',
            'copyright_text' => '© ' . date('Y') . ' Pixel and Code. All rights reserved.',
            'social_links' => [
                [
                    'id' => 'social-1',
                    'platform' => 'Facebook',
                    'icon' => 'fab-facebook',
                    'url' => 'https://facebook.com/pixelandcode'
                ],
                [
                    'id' => 'social-2',
                    'platform' => 'Twitter',
                    'icon' => 'fab-twitter',
                    'url' => 'https://twitter.com/pixelandcode'
                ],
                [
                    'id' => 'social-3',
                    'platform' => 'Instagram',
                    'icon' => 'fab-instagram',
                    'url' => 'https://instagram.com/pixelandcode'
                ],
                [
                    'id' => 'social-4',
                    'platform' => 'LinkedIn',
                    'icon' => 'fab-linkedin',
                    'url' => 'https://linkedin.com/company/pixelandcode'
                ]
            ],
            'quick_links' => [
                [
                    'id' => 'link-1',
                    'title' => 'Home',
                    'url' => '/'
                ],
                [
                    'id' => 'link-2',
                    'title' => 'About',
                    'url' => '/#about'
                ],
                [
                    'id' => 'link-3',
                    'title' => 'Services',
                    'url' => '/#services'
                ],
                [
                    'id' => 'link-4',
                    'title' => 'Portfolio',
                    'url' => '/#portfolio'
                ],
                [
                    'id' => 'link-5',
                    'title' => 'Contact',
                    'url' => '/#contact'
                ]
            ],
            'contact_info' => [
                'email' => '<EMAIL>',
                'phone' => '+****************',
                'address' => '123 Web Developer St, Silicon Valley, CA 94000'
            ],
            'is_active' => true
        ]);
    }
}
