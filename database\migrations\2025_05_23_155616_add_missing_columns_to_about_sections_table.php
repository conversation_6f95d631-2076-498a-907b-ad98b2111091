<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('about_sections', function (Blueprint $table) {
            // Add missing columns
            if (!Schema::hasColumn('about_sections', 'subtitle')) {
                $table->string('subtitle')->nullable();
            }
            if (!Schema::hasColumn('about_sections', 'content')) {
                $table->text('content')->nullable();
            }
            if (!Schema::hasColumn('about_sections', 'resume_link')) {
                $table->string('resume_link')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('about_sections', function (Blueprint $table) {
            $table->dropColumn(['subtitle', 'content', 'resume_link']);
        });
    }
};
