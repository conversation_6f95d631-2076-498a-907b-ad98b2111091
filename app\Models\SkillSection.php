<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SkillSection extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'name',
        'icon',
        'proficiency',
        'category',
        'order',
        'is_featured',
        'is_active'
    ];
    
    protected $casts = [
        'proficiency' => 'integer',
        'order' => 'integer',
        'is_featured' => 'boolean',
        'is_active' => 'boolean'
    ];
}
